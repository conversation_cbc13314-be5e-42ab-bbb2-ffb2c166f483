import { Injectable, inject } from '@angular/core';
import { CanActivate, Router, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { UserService } from '../services/user.service';

@Injectable({
  providedIn: 'root'
})
export class AuthGuard implements CanActivate {
  private router = inject(Router);
  private userService = inject(UserService);

  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): boolean {
    const user = this.userService.getCurrentUser();

    if (!user) {
      // 用户未登录，重定向到登录页面
      this.router.navigate(['/auth/login'], {
        queryParams: { returnUrl: state.url }
      });
      return false;
    }

    // 检查路由权限
    const requiredPermissions = route.data?.['permissions'] as string[];
    if (requiredPermissions && requiredPermissions.length > 0) {
      const hasPermission = this.userService.hasAnyPermission(requiredPermissions);
      if (!hasPermission) {
        // 用户没有权限，重定向到无权限页面或首页
        this.router.navigate(['/unauthorized']);
        return false;
      }
    }

    return true;
  }
}

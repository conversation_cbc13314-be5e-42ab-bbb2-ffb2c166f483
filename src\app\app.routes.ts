import { Routes } from '@angular/router';

export const routes: Routes = [
  // 默认路由重定向到仪表板
  {
    path: '',
    redirectTo: '/dashboard',
    pathMatch: 'full'
  },

  // 认证相关路由
  {
    path: 'auth',
    children: [
      {
        path: 'login',
        loadComponent: () => import('./views/auth/login/login.component').then(m => m.LoginComponent),
        title: '登录'
      },
      {
        path: '',
        redirectTo: 'login',
        pathMatch: 'full'
      }
    ]
  },

  // 仪表板路由
  {
    path: 'dashboard',
    loadComponent: () => import('./views/dashboard/dashboard.component').then(m => m.DashboardComponent),
    title: '仪表板'
  },

  // 用户相关路由
  {
    path: 'user',
    children: [
      {
        path: 'profile',
        loadComponent: () => import('./views/user/profile/profile.component').then(m => m.ProfileComponent),
        title: '个人资料'
      },
      {
        path: '',
        redirectTo: 'profile',
        pathMatch: 'full'
      }
    ]
  },

  // 无权限页面
  {
    path: 'unauthorized',
    loadComponent: () => import('./views/shared/unauthorized/unauthorized.component').then(m => m.UnauthorizedComponent),
    title: '访问被拒绝'
  },

  // 404 页面
  {
    path: '**',
    loadComponent: () => import('./views/shared/not-found/not-found.component').then(m => m.NotFoundComponent),
    title: '页面未找到'
  }
];

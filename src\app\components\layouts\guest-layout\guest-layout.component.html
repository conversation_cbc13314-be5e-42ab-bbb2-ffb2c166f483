<div class="guest-layout">
  <!-- 简单的顶部导航 -->
  <header class="guest-header">
    <div class="header-container">
      <div class="header-left">
        <h1 class="app-title">
          <a routerLink="/">我的应用</a>
        </h1>
      </div>
      
      <div class="header-right">
        <nav class="guest-nav">
          <a routerLink="/auth/login" class="nav-link">登录</a>
          <a routerLink="/auth/register" class="nav-link primary">注册</a>
        </nav>
      </div>
    </div>
  </header>

  <!-- 主内容区域 -->
  <main class="guest-main">
    <router-outlet></router-outlet>
  </main>

  <!-- 简单的底部 -->
  <footer class="guest-footer">
    <div class="footer-container">
      <p>&copy; 2024 我的应用. 保留所有权利.</p>
    </div>
  </footer>
</div>

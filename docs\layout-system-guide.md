# 基于页面类型的布局系统指南

## 概述

本项目采用基于页面类型的布局系统，而不是基于用户角色的布局。这种设计更加灵活，允许根据页面的功能和展示需求来选择合适的布局，而不是根据用户的角色。

## 布局类型

### 1. DefaultLayout - 标准布局
**适用场景**: 需要完整导航功能的标准应用页面

**特点**:
- 顶部导航栏 + 侧边栏导航
- 可折叠的侧边栏
- 完整的用户信息显示
- 响应式设计，支持移动端
- 适合复杂的管理界面和功能丰富的应用页面

**使用示例**:
```typescript
{
  id: 'main-dashboard',
  path: 'dashboard',
  title: '主仪表板',
  component: 'DashboardComponent',
  layout: 'DefaultLayout',
  permissions: ['user']
}
```

### 2. TopNavLayout - 顶部导航布局
**适用场景**: 简洁的应用页面，不需要侧边栏

**特点**:
- 仅顶部导航栏
- 水平导航菜单
- 支持下拉子菜单
- 简洁的用户界面
- 适合内容展示类页面

**使用示例**:
```typescript
{
  id: 'simple-reports',
  path: 'reports',
  title: '报表页面',
  component: 'ReportsComponent',
  layout: 'TopNavLayout',
  permissions: ['user']
}
```

### 3. BlankLayout - 空白布局
**适用场景**: 全屏页面、特殊展示页面

**特点**:
- 完全空白，无导航元素
- 全屏显示
- 适合数据可视化、打印页面、特殊功能页面
- 支持自定义样式类

**使用示例**:
```typescript
{
  id: 'fullscreen-chart',
  path: 'chart',
  title: '全屏图表',
  component: 'ChartComponent',
  layout: 'BlankLayout',
  permissions: ['user']
}
```

### 4. GuestLayout - 访客布局
**适用场景**: 登录、注册等公开页面

**特点**:
- 简洁的访客界面
- 渐变背景
- 适合认证相关页面
- 无需登录即可访问

**使用示例**:
```typescript
{
  id: 'login-page',
  path: 'login',
  title: '登录',
  component: 'LoginComponent',
  layout: 'GuestLayout'
}
```

## 布局选择原则

### 1. 功能复杂度
- **高复杂度**: 使用 DefaultLayout，提供完整的导航和功能
- **中等复杂度**: 使用 TopNavLayout，保持界面简洁
- **特殊需求**: 使用 BlankLayout，完全自定义

### 2. 用户交互需求
- **频繁导航**: DefaultLayout 的侧边栏导航更适合
- **偶尔导航**: TopNavLayout 的顶部导航足够
- **无需导航**: BlankLayout 专注内容展示

### 3. 内容展示需求
- **多功能面板**: DefaultLayout 提供更多空间组织
- **内容为主**: TopNavLayout 给内容更多空间
- **全屏展示**: BlankLayout 提供最大展示空间

## 配置示例

### 完整的路由配置示例
```typescript
const routeConfig: DynamicRouteConfig[] = [
  // 主应用 - 使用标准布局
  {
    id: 'main-app',
    path: 'app',
    title: '主应用',
    layout: 'DefaultLayout',
    permissions: ['user'],
    children: [
      {
        id: 'dashboard',
        path: 'dashboard',
        title: '仪表板',
        component: 'DashboardComponent',
        icon: '📊',
        order: 1
      },
      {
        id: 'users',
        path: 'users',
        title: '用户管理',
        component: 'UserListComponent',
        icon: '👥',
        order: 2,
        permissions: ['admin']
      }
    ]
  },
  
  // 简洁页面 - 使用顶部导航布局
  {
    id: 'simple-pages',
    path: 'simple',
    title: '简洁页面',
    layout: 'TopNavLayout',
    permissions: ['user'],
    children: [
      {
        id: 'reports',
        path: 'reports',
        title: '报表',
        component: 'ReportsComponent',
        icon: '📈',
        order: 1
      }
    ]
  },
  
  // 全屏页面 - 使用空白布局
  {
    id: 'fullscreen',
    path: 'fullscreen',
    title: '全屏页面',
    layout: 'BlankLayout',
    permissions: ['user'],
    children: [
      {
        id: 'visualization',
        path: 'viz',
        title: '数据可视化',
        component: 'VisualizationComponent'
      }
    ]
  }
];
```

## 自定义布局

### 添加新布局
1. 创建布局组件
2. 在 `DynamicRouteService` 中注册
3. 在路由配置中使用

```typescript
// 1. 创建组件
@Component({
  selector: 'app-custom-layout',
  template: `
    <div class="custom-layout">
      <!-- 自定义布局内容 -->
      <router-outlet></router-outlet>
    </div>
  `
})
export class CustomLayoutComponent {}

// 2. 注册到服务
private readonly layoutMapping: LayoutMapping = {
  'CustomLayout': () => import('./custom-layout.component').then(m => m.CustomLayoutComponent),
  // ...
};

// 3. 在路由中使用
{
  id: 'custom-page',
  path: 'custom',
  title: '自定义页面',
  layout: 'CustomLayout',
  component: 'CustomComponent'
}
```

## 布局间的导航

### 同一用户访问不同布局
用户可以在同一会话中访问使用不同布局的页面：

```typescript
// 用户可以访问这些不同布局的页面
'/app/dashboard'        // DefaultLayout
'/simple/reports'       // TopNavLayout  
'/fullscreen/viz'       // BlankLayout
```

### 导航菜单的处理
- DefaultLayout: 显示完整的侧边栏菜单
- TopNavLayout: 显示水平导航菜单
- BlankLayout: 不显示导航菜单
- GuestLayout: 显示简单的访客导航

## 最佳实践

### 1. 布局一致性
- 同类型的页面使用相同布局
- 保持用户体验的一致性
- 避免频繁切换布局类型

### 2. 性能考虑
- 布局组件使用懒加载
- 避免在布局中放置重型组件
- 合理使用 OnPush 变更检测

### 3. 响应式设计
- 所有布局都应支持移动端
- 使用合适的断点
- 考虑触摸设备的交互

### 4. 可访问性
- 提供键盘导航支持
- 使用语义化的 HTML 结构
- 支持屏幕阅读器

## 迁移指南

### 从基于角色的布局迁移
1. **分析现有页面**: 确定每个页面的实际需求
2. **重新分类**: 根据功能而非角色分组页面
3. **更新路由配置**: 使用新的布局名称
4. **测试验证**: 确保所有页面正常工作

### 迁移检查清单
- [ ] 删除旧的角色布局组件
- [ ] 创建新的页面类型布局
- [ ] 更新 DynamicRouteService 映射
- [ ] 修改路由配置示例
- [ ] 更新导航逻辑
- [ ] 测试所有布局功能
- [ ] 验证权限控制
- [ ] 检查响应式表现

这种基于页面类型的布局系统提供了更大的灵活性，使得同一用户可以根据不同的使用场景访问最适合的界面布局。

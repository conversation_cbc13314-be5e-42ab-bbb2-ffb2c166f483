import { Component, signal } from '@angular/core';
import { CommonModule } from '@angular/common';

interface UserListItem {
  id: string;
  name: string;
  email: string;
  role: string;
  status: 'active' | 'inactive';
  lastLogin: string;
}

@Component({
  selector: 'app-user-list',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './user-list.component.html',
  styleUrl: './user-list.component.css'
})
export class UserListComponent {
  protected readonly users = signal<UserListItem[]>([
    {
      id: '1',
      name: '张三',
      email: '<EMAIL>',
      role: 'admin',
      status: 'active',
      lastLogin: '2024-01-15 10:30:00'
    },
    {
      id: '2',
      name: '李四',
      email: '<EMAIL>',
      role: 'user',
      status: 'active',
      lastLogin: '2024-01-14 15:20:00'
    },
    {
      id: '3',
      name: '王五',
      email: '<EMAIL>',
      role: 'user',
      status: 'inactive',
      lastLogin: '2024-01-10 09:15:00'
    }
  ]);

  protected readonly isLoading = signal(false);

  editUser(userId: string): void {
    console.log('编辑用户:', userId);
  }

  deleteUser(userId: string): void {
    console.log('删除用户:', userId);
    // 实际项目中这里会调用 API
  }

  toggleUserStatus(userId: string): void {
    this.users.update(users => 
      users.map(user => 
        user.id === userId 
          ? { ...user, status: user.status === 'active' ? 'inactive' : 'active' }
          : user
      )
    );
  }
}

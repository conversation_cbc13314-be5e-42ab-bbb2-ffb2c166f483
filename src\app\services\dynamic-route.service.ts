import { Injectable, signal, computed, inject } from '@angular/core';
import { Router, Route } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { Observable, of, throwError } from 'rxjs';
import { map, catchError, tap, switchMap } from 'rxjs/operators';
import {
  DynamicRouteConfig,
  UserRouteResponse,
  ComponentMapping,
  LayoutMapping,
  NavigationItem,
  DynamicRouteState,
  RouteLoadingState,
  UserAccountType,
  UserInfo
} from '../models/dynamic-route.model';

@Injectable({
  providedIn: 'root'
})
export class DynamicRouteService {
  private router = inject(Router);
  private http = inject(HttpClient);

  // 状态管理
  private readonly _state = signal<DynamicRouteState>({
    routes: [],
    loadingState: 'idle',
    error: null,
    lastUpdated: null
  });

  // 公共只读状态
  readonly state = this._state.asReadonly();
  readonly routes = computed(() => this._state().routes);
  readonly loadingState = computed(() => this._state().loadingState);
  readonly error = computed(() => this._state().error);

  // 导航菜单项
  readonly navigationItems = computed(() => this.buildNavigationItems(this.routes()));

  // 组件映射 - 将字符串映射到实际的组件导入函数
  private readonly componentMapping: ComponentMapping = {
    // 页面组件
    'LoginComponent': () => import('../views/auth/login/login.component').then(m => m.LoginComponent),
    'DashboardComponent': () => import('../views/dashboard/dashboard.component').then(m => m.DashboardComponent),
    'ProfileComponent': () => import('../views/user/profile/profile.component').then(m => m.ProfileComponent),
    'NotFoundComponent': () => import('../views/shared/not-found/not-found.component').then(m => m.NotFoundComponent),
    'UserListComponent': () => import('../views/admin/user-list/user-list.component').then(m => m.UserListComponent),
    'LayoutDemoComponent': () => import('../views/demo/layout-demo/layout-demo.component').then(m => m.LayoutDemoComponent),
  };

  // 布局映射 - 基于页面类型而非用户角色
  private readonly layoutMapping: LayoutMapping = {
    'DefaultLayout': () => import('../components/layouts/default-layout/default-layout.component').then(m => m.DefaultLayoutComponent),
    'BlankLayout': () => import('../components/layouts/blank-layout/blank-layout.component').then(m => m.BlankLayoutComponent),
    'TopNavLayout': () => import('../components/layouts/top-nav-layout/top-nav-layout.component').then(m => m.TopNavLayoutComponent),
    'GuestLayout': () => import('../components/layouts/guest-layout/guest-layout.component').then(m => m.GuestLayoutComponent),
  };

  /**
   * 根据用户角色加载动态路由配置（新版本）
   */
  loadRoutesByRole(primaryRole: string, user: UserInfo): Observable<boolean> {
    this.setLoadingState('loading');

    return this.getRoutesByRole(primaryRole, user).pipe(
      tap(routeResponse => {
        this.applyDynamicRoutes(routeResponse.routes);
        this._state.update(state => ({
          ...state,
          routes: routeResponse.routes,
          loadingState: 'loaded',
          error: null,
          lastUpdated: new Date()
        }));
      }),
      map(() => true),
      catchError(error => {
        this.setLoadingState('error');
        this._state.update(state => ({ ...state, error: error.message }));
        return of(false);
      })
    );
  }

  /**
   * 根据账号类型加载动态路由配置（兼容性方法）
   * @deprecated 使用 loadRoutesByRole() 替代
   */
  loadRoutesByAccountType(accountType: UserAccountType): Observable<boolean> {
    // 兼容性实现：将账号类型映射为角色
    const roleMapping: Record<UserAccountType, string> = {
      'admin': 'admin',
      'user': 'user',
      'guest': 'guest'
    };

    const mockUser: UserInfo = {
      id: 'legacy_user',
      name: '兼容用户',
      email: '<EMAIL>',
      roles: [roleMapping[accountType]],
      permissions: []
    };

    return this.loadRoutesByRole(roleMapping[accountType], mockUser);
  }

  /**
   * 从后端获取用户的动态路由配置
   */
  loadUserRoutes(userId: string): Observable<UserRouteResponse> {
    this.setLoadingState('loading');

    // 模拟 API 调用 - 实际项目中替换为真实的 API 端点
    return this.mockApiCall(userId).pipe(
      tap(response => {
        this._state.update(state => ({
          ...state,
          routes: response.routes,
          loadingState: 'loaded',
          error: null,
          lastUpdated: new Date()
        }));
      }),
      catchError(error => {
        this.setLoadingState('error');
        this._state.update(state => ({
          ...state,
          error: error.message || '加载路由配置失败'
        }));
        return throwError(() => error);
      })
    );
  }

  /**
   * 应用动态路由到 Angular Router
   */
  applyDynamicRoutes(routeConfigs: DynamicRouteConfig[]): void {
    try {
      const dynamicRoutes = this.convertToAngularRoutes(routeConfigs);

      // 获取当前路由配置
      const currentRoutes = this.router.config;

      // 移除之前的动态路由（保留静态路由如登录、404等）
      const staticRoutes = currentRoutes.filter(route =>
        route.path === 'auth' ||
        route.path === '' ||
        route.path === '**'
      );

      // 合并静态路由和动态路由
      const newRoutes = [...staticRoutes.slice(0, -1), ...dynamicRoutes, staticRoutes[staticRoutes.length - 1]];

      // 重置路由配置
      this.router.resetConfig(newRoutes);

      console.log('动态路由已应用:', newRoutes);
    } catch (error) {
      console.error('应用动态路由失败:', error);
      throw error;
    }
  }

  /**
   * 将动态路由配置转换为 Angular Route 对象
   */
  private convertToAngularRoutes(routeConfigs: DynamicRouteConfig[]): Route[] {
    return routeConfigs.map(config => this.convertSingleRoute(config));
  }

  /**
   * 转换单个路由配置
   */
  private convertSingleRoute(config: DynamicRouteConfig): Route {
    const route: Route = {
      path: config.path,
      data: {
        title: config.title,
        permissions: config.permissions,
        ...config.data
      }
    };

    // 处理重定向
    if (config.redirectTo) {
      route.redirectTo = config.redirectTo;
      route.pathMatch = config.pathMatch || 'full';
      return route;
    }

    // 处理布局
    if (config.layout) {
      const layoutLoader = this.layoutMapping[config.layout];
      if (layoutLoader) {
        route.loadComponent = layoutLoader;

        // 如果有子路由，添加到 children
        if (config.children && config.children.length > 0) {
          route.children = this.convertToAngularRoutes(config.children);
        }
      } else {
        console.warn(`未找到布局组件: ${config.layout}`);
      }
    } else {
      // 处理普通组件
      const componentLoader = this.componentMapping[config.component];
      if (componentLoader) {
        route.loadComponent = componentLoader;
      } else {
        console.warn(`未找到组件: ${config.component}`);
        // 使用默认的 404 组件
        route.loadComponent = this.componentMapping['NotFoundComponent'];
      }
    }

    // 处理子路由
    if (config.children && config.children.length > 0 && !config.layout) {
      route.children = this.convertToAngularRoutes(config.children);
    }

    return route;
  }

  /**
   * 构建导航菜单项
   */
  private buildNavigationItems(routes: DynamicRouteConfig[]): NavigationItem[] {
    return routes
      .filter(route => route.title && !route.redirectTo) // 过滤掉重定向路由
      .map(route => ({
        id: route.id,
        title: route.title,
        path: route.path,
        icon: route.icon,
        order: route.order || 0,
        permissions: route.permissions,
        children: route.children ? this.buildNavigationItems(route.children) : undefined
      }))
      .sort((a, b) => a.order - b.order);
  }

  /**
   * 设置加载状态
   */
  private setLoadingState(state: RouteLoadingState): void {
    this._state.update(current => ({
      ...current,
      loadingState: state
    }));
  }

  /**
   * 模拟 API 调用 - 实际项目中替换为真实的 HTTP 请求
   */
  private mockApiCall(userId: string): Observable<UserRouteResponse> {
    // 根据用户ID判断账号类型
    const accountType = this.getAccountTypeFromUserId(userId);

    // 根据账号类型返回不同的路由配置
    const mockResponse: UserRouteResponse = this.getRoutesByAccountType(accountType);

    return of(mockResponse).pipe(
      map(response => response),
      catchError(error => throwError(() => error))
    );
  }

  /**
   * 根据用户ID获取账号类型
   */
  private getAccountTypeFromUserId(userId: string): UserAccountType {
    if (userId.includes('admin')) return 'admin';
    if (userId.includes('guest')) return 'guest';
    return 'user';
  }

  /**
   * 根据用户角色获取路由配置（新版本）
   */
  private getRoutesByRole(primaryRole: string, user: UserInfo): Observable<UserRouteResponse> {
    // 模拟从后端获取路由配置
    return of(null).pipe(
      map(() => {
        // 根据角色返回不同的路由配置
        switch (primaryRole) {
          case 'admin':
            return this.getAdminRoutes();
          case 'manager':
            return this.getManagerRoutes();
          case 'editor':
            return this.getEditorRoutes();
          case 'user':
            return this.getUserRoutes();
          case 'viewer':
            return this.getViewerRoutes();
          case 'guest':
            return this.getGuestRoutes();
          default:
            return this.getDefaultRoutes(user);
        }
      })
    );
  }

  /**
   * 根据账号类型获取路由配置（兼容性方法）
   * @deprecated 使用 getRoutesByRole() 替代
   */
  private getRoutesByAccountType(accountType: UserAccountType): UserRouteResponse {
    switch (accountType) {
      case 'admin':
        return this.getAdminRoutes();
      case 'guest':
        return this.getGuestRoutes();
      case 'user':
      default:
        return this.getUserRoutes();
    }
  }

  /**
   * 获取管理员路由配置
   */
  private getAdminRoutes(): UserRouteResponse {
    return {
      routes: [
        {
          id: 'admin-app',
          path: 'admin',
          title: '管理后台',
          component: '',
          layout: 'DefaultLayout',
          permissions: ['admin'],
          children: [
            {
              id: 'admin-dashboard',
              path: '',
              title: '管理仪表板',
              component: 'DashboardComponent',
              redirectTo: 'dashboard',
              pathMatch: 'full'
            },
            {
              id: 'admin-dashboard-main',
              path: 'dashboard',
              title: '管理仪表板',
              component: 'DashboardComponent',
              icon: '📊',
              order: 1,
              permissions: ['admin']
            },
            {
              id: 'admin-users',
              path: 'users',
              title: '用户管理',
              component: 'UserListComponent',
              icon: '👥',
              order: 2,
              permissions: ['admin', 'user-management']
            },
            {
              id: 'admin-layout-demo',
              path: 'layout-demo',
              title: '布局演示',
              component: 'LayoutDemoComponent',
              icon: '🎨',
              order: 3,
              permissions: ['admin']
            }
          ]
        }
      ],
      layouts: [
        { name: 'DefaultLayout', component: 'DefaultLayout', description: '标准布局 - 完整导航和侧边栏' },
        { name: 'TopNavLayout', component: 'TopNavLayout', description: '顶部导航布局 - 简洁的顶部导航' },
        { name: 'BlankLayout', component: 'BlankLayout', description: '空白布局 - 全屏或特殊页面' },
        { name: 'GuestLayout', component: 'GuestLayout', description: '访客布局 - 登录注册页面' }
      ],
      permissions: ['admin', 'user-management', 'system-settings'],
      user: {
        id: 'admin_user',
        name: '系统管理员',
        role: 'admin',
        permissions: ['admin', 'user-management', 'system-settings']
      }
    };
  }

  /**
   * 获取普通用户路由配置
   */
  private getUserRoutes(): UserRouteResponse {
    return {
      routes: [
        {
          id: 'user-app',
          path: 'app',
          title: '用户应用',
          component: '',
          layout: 'TopNavLayout',
          permissions: ['user'],
          children: [
            {
              id: 'user-dashboard',
              path: '',
              title: '个人仪表板',
              component: 'DashboardComponent',
              redirectTo: 'dashboard',
              pathMatch: 'full'
            },
            {
              id: 'user-dashboard-main',
              path: 'dashboard',
              title: '个人仪表板',
              component: 'DashboardComponent',
              icon: '📊',
              order: 1,
              permissions: ['user']
            },
            {
              id: 'user-profile',
              path: 'profile',
              title: '个人资料',
              component: 'ProfileComponent',
              icon: '👤',
              order: 2,
              permissions: ['user']
            }
          ]
        }
      ],
      layouts: [
        { name: 'DefaultLayout', component: 'DefaultLayout', description: '标准布局 - 完整导航和侧边栏' },
        { name: 'TopNavLayout', component: 'TopNavLayout', description: '顶部导航布局 - 简洁的顶部导航' },
        { name: 'BlankLayout', component: 'BlankLayout', description: '空白布局 - 全屏或特殊页面' },
        { name: 'GuestLayout', component: 'GuestLayout', description: '访客布局 - 登录注册页面' }
      ],
      permissions: ['user'],
      user: {
        id: 'user_user',
        name: '普通用户',
        role: 'user',
        permissions: ['user']
      }
    };
  }

  /**
   * 获取访客路由配置
   */
  private getGuestRoutes(): UserRouteResponse {
    return {
      routes: [
        {
          id: 'guest-app',
          path: 'guest',
          title: '访客页面',
          component: '',
          layout: 'TopNavLayout',
          permissions: ['guest'],
          children: [
            {
              id: 'guest-dashboard',
              path: '',
              title: '公开内容',
              component: 'DashboardComponent',
              redirectTo: 'dashboard',
              pathMatch: 'full'
            },
            {
              id: 'guest-dashboard-main',
              path: 'dashboard',
              title: '公开内容',
              component: 'DashboardComponent',
              icon: '👁️',
              order: 1,
              permissions: ['guest']
            }
          ]
        }
      ],
      layouts: [
        { name: 'DefaultLayout', component: 'DefaultLayout', description: '标准布局 - 完整导航和侧边栏' },
        { name: 'TopNavLayout', component: 'TopNavLayout', description: '顶部导航布局 - 简洁的顶部导航' },
        { name: 'BlankLayout', component: 'BlankLayout', description: '空白布局 - 全屏或特殊页面' },
        { name: 'GuestLayout', component: 'GuestLayout', description: '访客布局 - 登录注册页面' }
      ],
      permissions: ['guest'],
      user: {
        id: 'guest_user',
        name: '访客用户',
        role: 'guest',
        permissions: ['guest']
      }
    };
  }

  /**
   * 获取经理路由配置
   */
  private getManagerRoutes(): UserRouteResponse {
    return {
      routes: [
        {
          id: 'management-app',
          path: 'management',
          title: '管理中心',
          component: '',
          layout: 'DefaultLayout',
          permissions: ['management'],
          children: [
            {
              id: 'management-dashboard',
              path: '',
              title: '管理仪表板',
              component: 'DashboardComponent',
              redirectTo: 'dashboard',
              pathMatch: 'full'
            },
            {
              id: 'management-dashboard-main',
              path: 'dashboard',
              title: '管理仪表板',
              component: 'DashboardComponent',
              icon: '📊',
              order: 1,
              permissions: ['management']
            },
            {
              id: 'team-management',
              path: 'team',
              title: '团队管理',
              component: 'UserListComponent',
              icon: '👥',
              order: 2,
              permissions: ['team-lead']
            }
          ]
        }
      ],
      layouts: [
        { name: 'DefaultLayout', component: 'DefaultLayout', description: '标准布局 - 完整导航和侧边栏' }
      ],
      permissions: ['management', 'team-lead'],
      user: {
        id: 'manager_user',
        name: '部门经理',
        role: 'manager',
        permissions: ['management', 'team-lead']
      }
    };
  }

  /**
   * 获取编辑者路由配置
   */
  private getEditorRoutes(): UserRouteResponse {
    return {
      routes: [
        {
          id: 'editor-app',
          path: 'editor',
          title: '内容编辑',
          component: '',
          layout: 'TopNavLayout',
          permissions: ['edit-content'],
          children: [
            {
              id: 'editor-dashboard',
              path: '',
              title: '编辑工作台',
              component: 'DashboardComponent',
              redirectTo: 'dashboard',
              pathMatch: 'full'
            },
            {
              id: 'editor-dashboard-main',
              path: 'dashboard',
              title: '编辑工作台',
              component: 'DashboardComponent',
              icon: '✏️',
              order: 1,
              permissions: ['edit-content']
            },
            {
              id: 'content-management',
              path: 'content',
              title: '内容管理',
              component: 'ProfileComponent',
              icon: '📝',
              order: 2,
              permissions: ['edit-content']
            }
          ]
        }
      ],
      layouts: [
        { name: 'TopNavLayout', component: 'TopNavLayout', description: '顶部导航布局 - 简洁的顶部导航' }
      ],
      permissions: ['edit-content', 'view-data'],
      user: {
        id: 'editor_user',
        name: '内容编辑',
        role: 'editor',
        permissions: ['edit-content', 'view-data']
      }
    };
  }

  /**
   * 获取查看者路由配置
   */
  private getViewerRoutes(): UserRouteResponse {
    return {
      routes: [
        {
          id: 'viewer-app',
          path: 'view',
          title: '数据查看',
          component: '',
          layout: 'TopNavLayout',
          permissions: ['view-only'],
          children: [
            {
              id: 'viewer-dashboard',
              path: '',
              title: '数据仪表板',
              component: 'DashboardComponent',
              redirectTo: 'dashboard',
              pathMatch: 'full'
            },
            {
              id: 'viewer-dashboard-main',
              path: 'dashboard',
              title: '数据仪表板',
              component: 'DashboardComponent',
              icon: '📈',
              order: 1,
              permissions: ['view-only']
            }
          ]
        }
      ],
      layouts: [
        { name: 'TopNavLayout', component: 'TopNavLayout', description: '顶部导航布局 - 简洁的顶部导航' }
      ],
      permissions: ['view-only'],
      user: {
        id: 'viewer_user',
        name: '数据查看者',
        role: 'viewer',
        permissions: ['view-only']
      }
    };
  }

  /**
   * 获取默认路由配置（用于未知角色）
   */
  private getDefaultRoutes(user: UserInfo): UserRouteResponse {
    return {
      routes: [
        {
          id: 'default-app',
          path: 'dashboard',
          title: '默认仪表板',
          component: 'DashboardComponent',
          layout: 'TopNavLayout',
          permissions: ['basic']
        }
      ],
      layouts: [
        { name: 'TopNavLayout', component: 'TopNavLayout', description: '顶部导航布局 - 简洁的顶部导航' }
      ],
      permissions: ['basic'],
      user: {
        id: user.id,
        name: user.name,
        role: 'unknown',
        permissions: ['basic']
      }
    };
  }

  /**
   * 清除动态路由
   */
  clearDynamicRoutes(): void {
    // 重置路由配置为基本路由
    this.router.resetConfig([
      { path: '', redirectTo: '/auth/login', pathMatch: 'full' },
      { path: 'auth/login', loadComponent: () => import('../views/auth/login/login.component').then(m => m.LoginComponent) },
      { path: '**', loadComponent: () => import('../views/shared/not-found/not-found.component').then(m => m.NotFoundComponent) }
    ]);

    // 重置状态
    this._state.set({
      routes: [],
      loadingState: 'idle',
      error: null,
      lastUpdated: null
    });
  }

  /**
   * 根据用户权限过滤路由
   */
  private filterRoutesByPermissions(routes: DynamicRouteConfig[], userPermissions: string[]): DynamicRouteConfig[] {
    return routes.filter(route => {
      // 检查当前路由权限
      if (route.permissions && route.permissions.length > 0) {
        const hasPermission = route.permissions.some(permission =>
          userPermissions.includes(permission)
        );
        if (!hasPermission) return false;
      }

      // 递归过滤子路由
      if (route.children) {
        route.children = this.filterRoutesByPermissions(route.children, userPermissions);
      }

      return true;
    });
  }



  /**
   * 添加组件映射
   */
  addComponentMapping(name: string, loader: () => Promise<any>): void {
    this.componentMapping[name] = loader;
  }

  /**
   * 添加布局映射
   */
  addLayoutMapping(name: string, loader: () => Promise<any>): void {
    this.layoutMapping[name] = loader;
  }
}

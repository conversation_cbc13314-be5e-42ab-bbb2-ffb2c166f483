<div class="user-list-container">
  <div class="page-header">
    <h1>用户管理</h1>
    <button class="btn btn-primary">
      <i class="icon-plus"></i>
      添加用户
    </button>
  </div>

  <div class="user-table-container">
    @if (isLoading()) {
      <div class="loading">
        <div class="spinner"></div>
        <p>加载中...</p>
      </div>
    } @else {
      <table class="user-table">
        <thead>
          <tr>
            <th>用户名</th>
            <th>邮箱</th>
            <th>角色</th>
            <th>状态</th>
            <th>最后登录</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          @for (user of users(); track user.id) {
            <tr>
              <td>{{ user.name }}</td>
              <td>{{ user.email }}</td>
              <td>
                <span class="role-badge" [class]="'role-' + user.role">
                  {{ user.role === 'admin' ? '管理员' : '普通用户' }}
                </span>
              </td>
              <td>
                <span class="status-badge" [class]="'status-' + user.status">
                  {{ user.status === 'active' ? '活跃' : '禁用' }}
                </span>
              </td>
              <td>{{ user.lastLogin }}</td>
              <td>
                <div class="action-buttons">
                  <button 
                    class="btn btn-sm btn-outline" 
                    (click)="editUser(user.id)"
                    title="编辑"
                  >
                    <i class="icon-edit"></i>
                  </button>
                  <button 
                    class="btn btn-sm btn-outline" 
                    (click)="toggleUserStatus(user.id)"
                    [title]="user.status === 'active' ? '禁用' : '启用'"
                  >
                    <i class="icon-toggle"></i>
                  </button>
                  <button 
                    class="btn btn-sm btn-danger" 
                    (click)="deleteUser(user.id)"
                    title="删除"
                  >
                    <i class="icon-delete"></i>
                  </button>
                </div>
              </td>
            </tr>
          }
        </tbody>
      </table>
    }
  </div>
</div>

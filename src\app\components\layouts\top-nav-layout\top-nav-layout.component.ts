import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterOutlet, RouterLink, RouterLinkActive } from '@angular/router';
import { DynamicRouteService } from '../../../services/dynamic-route.service';
import { UserService } from '../../../services/user.service';

@Component({
  selector: 'app-top-nav-layout',
  standalone: true,
  imports: [CommonModule, RouterOutlet, RouterLink, RouterLinkActive],
  templateUrl: './top-nav-layout.component.html',
  styleUrl: './top-nav-layout.component.css'
})
export class TopNavLayoutComponent {
  private dynamicRouteService = inject(DynamicRouteService);
  private userService = inject(UserService);

  protected readonly navigationItems = this.dynamicRouteService.navigationItems;
  protected readonly currentUser = this.userService.getCurrentUser;

  logout(): void {
    this.userService.logout();
  }

  hasPermission(permissions?: string[]): boolean {
    if (!permissions || permissions.length === 0) return true;
    
    const userPermissions = this.currentUser()?.permissions || [];
    return permissions.some(permission => userPermissions.includes(permission));
  }
}

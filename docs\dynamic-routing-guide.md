# Angular 动态路由系统实现指南

## 概述

本项目实现了一个完整的 Angular 动态路由系统，支持：
- 用户登录后从后端获取路由配置
- 运行时动态注册路由
- 多布局支持（管理员、用户、访客布局）
- 基于权限的路由访问控制
- 自动导航菜单生成

## 核心架构

### 1. 数据结构设计

#### DynamicRouteConfig
```typescript
interface DynamicRouteConfig {
  id: string;              // 路由唯一标识
  path: string;            // 路由路径
  title: string;           // 路由标题
  component: string;       // 组件名称
  layout?: string;         // 布局组件名称
  icon?: string;           // 图标
  order?: number;          // 排序
  permissions?: string[];  // 所需权限
  children?: DynamicRouteConfig[];  // 子路由
}
```

#### UserRouteResponse
```typescript
interface UserRouteResponse {
  routes: DynamicRouteConfig[];  // 用户可访问的路由
  layouts: LayoutConfig[];       // 可用布局
  permissions: string[];         // 用户权限列表
  user: UserInfo;               // 用户信息
}
```

### 2. 服务架构

#### DynamicRouteService
- 负责从后端获取路由配置
- 将路由配置转换为 Angular Route 对象
- 使用 `router.resetConfig()` 动态更新路由
- 管理组件和布局的映射关系
- 生成导航菜单项

#### UserService
- 处理用户登录/登出
- 管理用户状态和权限
- 与 DynamicRouteService 协作加载路由
- 本地存储用户信息

### 3. 布局系统

#### 管理员布局 (AdminLayoutComponent)
- 侧边栏导航
- 可折叠菜单
- 用户信息显示
- 适用于管理后台

#### 用户布局 (UserLayoutComponent)
- 顶部导航栏
- 简洁的用户界面
- 适用于普通用户应用

#### 访客布局 (GuestLayoutComponent)
- 最简布局
- 适用于登录、注册等公开页面

## 使用方法

### 1. 后端 API 设计

后端需要提供一个接口返回用户的路由配置：

```typescript
// GET /api/user/{userId}/routes
{
  "routes": [
    {
      "id": "admin-layout",
      "path": "admin",
      "title": "管理后台",
      "component": "",
      "layout": "AdminLayoutComponent",
      "permissions": ["admin"],
      "children": [
        {
          "id": "admin-dashboard",
          "path": "dashboard",
          "title": "管理仪表板",
          "component": "DashboardComponent",
          "icon": "dashboard",
          "order": 1,
          "permissions": ["admin"]
        }
      ]
    }
  ],
  "layouts": [...],
  "permissions": ["admin", "user"],
  "user": {...}
}
```

### 2. 添加新组件

1. 创建组件文件
2. 在 `DynamicRouteService` 中添加组件映射：

```typescript
private readonly componentMapping: ComponentMapping = {
  'YourNewComponent': () => import('./path/to/your-component').then(m => m.YourNewComponent),
  // ...
};
```

### 3. 添加新布局

1. 创建布局组件
2. 在 `DynamicRouteService` 中添加布局映射：

```typescript
private readonly layoutMapping: LayoutMapping = {
  'YourNewLayout': () => import('./path/to/your-layout').then(m => m.YourNewLayout),
  // ...
};
```

### 4. 权限控制

使用 `AuthGuard` 和 `PermissionGuard` 保护路由：

```typescript
// 在路由配置中
{
  path: 'admin',
  canActivate: [AuthGuard, PermissionGuard],
  data: { 
    permissions: ['admin'],
    roles: ['admin'] 
  }
}
```

## 工作流程

### 1. 用户登录流程

1. 用户在登录页面输入凭据
2. `UserService.login()` 验证用户身份
3. 登录成功后调用 `DynamicRouteService.loadUserRoutes()`
4. 从后端获取用户的路由配置
5. 调用 `DynamicRouteService.applyDynamicRoutes()` 应用路由
6. 根据用户角色自动导航到相应页面

### 2. 路由保护流程

1. 用户访问受保护的路由
2. `AuthGuard` 检查用户是否已登录
3. `PermissionGuard` 检查用户是否有相应权限
4. 如果检查失败，重定向到登录页面或无权限页面

### 3. 导航菜单生成

1. `DynamicRouteService` 根据路由配置生成导航项
2. 布局组件订阅导航项信号
3. 根据用户权限过滤显示的菜单项
4. 自动排序和分组菜单项

## 最佳实践

### 1. 路由设计

- 使用有意义的路由 ID
- 合理设置路由层级
- 为每个路由设置适当的权限
- 使用图标和排序优化用户体验

### 2. 权限管理

- 采用最小权限原则
- 权限粒度要适中
- 支持角色和权限的组合使用
- 在前后端都进行权限验证

### 3. 性能优化

- 使用懒加载减少初始包大小
- 合理使用 Angular Signals 管理状态
- 避免不必要的路由重新配置
- 缓存用户信息和路由配置

### 4. 错误处理

- 提供友好的错误页面
- 处理网络请求失败的情况
- 记录错误日志便于调试
- 提供用户反馈机制

## 扩展功能

### 1. 路由缓存

可以添加路由配置缓存，避免每次登录都重新获取：

```typescript
// 在 DynamicRouteService 中
private cacheRoutes(userId: string, routes: DynamicRouteConfig[]): void {
  const cacheKey = `routes_${userId}`;
  localStorage.setItem(cacheKey, JSON.stringify(routes));
}
```

### 2. 动态权限更新

支持在用户会话期间动态更新权限：

```typescript
updateUserPermissions(newPermissions: string[]): void {
  // 更新用户权限
  // 重新过滤路由
  // 更新导航菜单
}
```

### 3. 路由分析

添加路由访问统计和分析功能：

```typescript
trackRouteAccess(routeId: string): void {
  // 记录路由访问
  // 发送分析数据
}
```

## 故障排除

### 常见问题

1. **路由不生效**
   - 检查组件映射是否正确
   - 确认路由配置格式正确
   - 验证权限设置

2. **权限检查失败**
   - 确认用户权限数据正确
   - 检查守卫配置
   - 验证路由数据设置

3. **布局显示异常**
   - 检查布局组件导入
   - 确认布局映射配置
   - 验证 CSS 样式

### 调试技巧

- 使用浏览器开发者工具查看路由配置
- 在控制台查看错误信息
- 使用 Angular DevTools 调试状态
- 检查网络请求和响应

## 总结

这个动态路由系统提供了一个完整的企业级解决方案，支持灵活的路由配置、权限控制和多布局系统。通过合理的架构设计和最佳实践，可以满足大多数企业应用的需求。

import { Component, signal, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterOutlet, RouterLink, RouterLinkActive } from '@angular/router';
import { DynamicRouteService } from '../../../services/dynamic-route.service';
import { UserService } from '../../../services/user.service';
import { NavigationItem } from '../../../models/dynamic-route.model';

@Component({
  selector: 'app-default-layout',
  standalone: true,
  imports: [CommonModule, RouterOutlet, RouterLink, RouterLinkActive],
  templateUrl: './default-layout.component.html',
  styleUrl: './default-layout.component.css'
})
export class DefaultLayoutComponent {
  private dynamicRouteService = inject(DynamicRouteService);
  private userService = inject(UserService);

  protected readonly navigationItems = this.dynamicRouteService.navigationItems;
  protected readonly currentUser = this.userService.getCurrentUser;
  protected readonly isSidebarCollapsed = signal(false);
  protected readonly isMobileMenuOpen = signal(false);

  toggleSidebar(): void {
    this.isSidebarCollapsed.update(collapsed => !collapsed);
  }

  toggleMobileMenu(): void {
    this.isMobileMenuOpen.update(open => !open);
  }

  logout(): void {
    this.userService.logout();
  }

  hasPermission(permissions?: string[]): boolean {
    if (!permissions || permissions.length === 0) return true;
    
    const userPermissions = this.currentUser()?.permissions || [];
    return permissions.some(permission => userPermissions.includes(permission));
  }

  closeMobileMenu(): void {
    this.isMobileMenuOpen.set(false);
  }
}

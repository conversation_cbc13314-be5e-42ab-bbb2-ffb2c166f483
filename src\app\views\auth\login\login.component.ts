import { Component, signal, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { UserService } from '../../../services/user.service';
import { LoginCredentials } from '../../../models/dynamic-route.model';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './login.component.html',
  styleUrl: './login.component.css'
})
export class LoginComponent {
  private router = inject(Router);
  private userService = inject(UserService);

  // 表单状态
  protected readonly email = signal('');
  protected readonly password = signal('');
  protected readonly isLoading = signal(false);
  protected readonly errorMessage = signal('');

  // 演示账号（用于快速测试）
  protected readonly demoAccounts = [
    {
      name: '系统管理员',
      email: '<EMAIL>',
      password: 'admin123',
      description: '完整的系统管理权限，可以管理用户、系统设置等',
      roles: ['admin', 'manager']
    },
    {
      name: '部门经理',
      email: '<EMAIL>',
      password: 'manager123',
      description: '部门管理权限，可以管理团队和内容编辑',
      roles: ['manager', 'editor']
    },
    {
      name: '普通用户',
      email: '<EMAIL>',
      password: 'user123',
      description: '基本的应用使用权限，可以查看个人信息、使用基本功能',
      roles: ['user']
    },
    {
      name: '访客用户',
      email: '<EMAIL>',
      password: 'guest123',
      description: '有限的浏览权限，只能查看公开内容',
      roles: ['guest']
    }
  ];

  /**
   * 表单输入处理
   */
  onEmailChange(value: string): void {
    this.email.set(value);
    this.errorMessage.set('');
  }

  onPasswordChange(value: string): void {
    this.password.set(value);
    this.errorMessage.set('');
  }

  /**
   * 使用演示账号
   */
  useDemoAccount(demoAccount: any): void {
    this.email.set(demoAccount.email);
    this.password.set(demoAccount.password);
    this.errorMessage.set('');
  }

  /**
   * 提交登录表单
   */
  onSubmit(): void {
    if (!this.email() || !this.password()) {
      this.errorMessage.set('请输入邮箱和密码');
      return;
    }

    this.isLoading.set(true);
    this.errorMessage.set('');

    const credentials: LoginCredentials = {
      email: this.email(),
      password: this.password()
    };

    // 使用新的动态角色登录方法
    this.userService.login(credentials).subscribe({
      next: (success) => {
        console.log('登录成功，系统将根据用户角色自动分配相应的界面和权限');
        this.isLoading.set(false);
        // 登录成功后，用户服务会自动导航到相应页面
      },
      error: (error) => {
        console.error('登录失败:', error);
        this.isLoading.set(false);
        this.errorMessage.set(error.message || '登录失败，请检查用户名和密码');
      }
    });
  }
}

import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';

interface LayoutDemo {
  name: string;
  description: string;
  features: string[];
  demoPath: string;
  layoutType: string;
  useCase: string;
}

@Component({
  selector: 'app-layout-demo',
  standalone: true,
  imports: [CommonModule, RouterLink],
  templateUrl: './layout-demo.component.html',
  styleUrl: './layout-demo.component.css'
})
export class LayoutDemoComponent {
  showCode(layoutName: string): void {
    console.log(`显示 ${layoutName} 的代码示例`);
    // 这里可以实现显示代码的逻辑，比如打开模态框
  }

  protected readonly layouts: LayoutDemo[] = [
    {
      name: 'DefaultLayout',
      description: '标准布局 - 完整的导航和侧边栏',
      features: [
        '顶部导航栏',
        '可折叠侧边栏',
        '完整用户信息',
        '响应式设计',
        '移动端支持'
      ],
      demoPath: '/app/dashboard',
      layoutType: 'DefaultLayout',
      useCase: '复杂的管理界面和功能丰富的应用页面'
    },
    {
      name: 'TopNavLayout',
      description: '顶部导航布局 - 简洁的水平导航',
      features: [
        '顶部导航栏',
        '水平菜单',
        '下拉子菜单',
        '简洁界面',
        '内容优先'
      ],
      demoPath: '/simple/dashboard',
      layoutType: 'TopNavLayout',
      useCase: '内容展示类页面和简洁的应用界面'
    },
    {
      name: 'BlankLayout',
      description: '空白布局 - 全屏显示无导航',
      features: [
        '完全空白',
        '全屏显示',
        '无导航元素',
        '自定义样式',
        '专注内容'
      ],
      demoPath: '/fullscreen/dashboard',
      layoutType: 'BlankLayout',
      useCase: '数据可视化、打印页面、特殊功能页面'
    },
    {
      name: 'GuestLayout',
      description: '访客布局 - 登录注册页面',
      features: [
        '简洁访客界面',
        '渐变背景',
        '认证页面',
        '无需登录',
        '品牌展示'
      ],
      demoPath: '/auth/login',
      layoutType: 'GuestLayout',
      useCase: '登录、注册、忘记密码等公开页面'
    }
  ];
}

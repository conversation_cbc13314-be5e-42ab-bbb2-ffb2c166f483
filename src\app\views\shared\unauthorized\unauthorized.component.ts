import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';
import { UserService } from '../../../services/user.service';

@Component({
  selector: 'app-unauthorized',
  standalone: true,
  imports: [CommonModule, RouterLink],
  templateUrl: './unauthorized.component.html',
  styleUrl: './unauthorized.component.css'
})
export class UnauthorizedComponent {
  private userService = inject(UserService);

  protected readonly currentUser = this.userService.getCurrentUser;

  goBack(): void {
    window.history.back();
  }

  goHome(): void {
    // 基于页面类型的导航，统一导航到主应用
    window.location.href = '/app';
  }
}

# Angular 企业级项目结构指南

## 概述

本文档描述了我们 Angular 项目的企业级目录结构，遵循 Angular 风格指南和最佳实践，旨在提供可扩展、可维护的代码组织方式。

## 目录结构

```
src/app/
├── views/                    # 页面级组件目录
│   ├── auth/                # 认证相关页面
│   │   ├── login/
│   │   ├── register/
│   │   └── forgot-password/
│   ├── dashboard/           # 仪表板页面
│   ├── user/               # 用户管理页面
│   │   ├── profile/
│   │   └── settings/
│   ├── admin/              # 管理员页面
│   │   ├── users/
│   │   └── settings/
│   └── shared/             # 共享的页面级组件
│       └── not-found/
├── components/             # 可复用 UI 组件
├── services/              # 应用服务
├── guards/                # 路由守卫
├── interceptors/          # HTTP 拦截器
├── models/                # 数据模型和类型定义
├── utils/                 # 工具函数
├── core/                  # 核心模块
├── app.config.ts          # 应用配置
├── app.routes.ts          # 路由配置
└── app.ts                 # 根组件
```

## 目录说明

### 1. views/ - 页面级组件

**用途**: 存放所有页面级组件，按功能模块组织

**命名约定**:

- 目录名使用 kebab-case
- 组件文件遵循 Angular CLI 约定：`component-name.component.ts`

**组织原则**:

- 按业务功能分组（如 auth、user、admin）
- 每个页面组件包含完整的文件集：
  - `*.component.ts` - 组件逻辑
  - `*.component.html` - 模板
  - `*.component.css` - 样式
  - `*.component.spec.ts` - 测试文件

**示例**:

```
views/auth/login/
├── login.component.ts
├── login.component.html
├── login.component.css
└── login.component.spec.ts
```

### 2. components/ - 可复用组件

**用途**: 存放可在多个页面中复用的 UI 组件

**组织方式**:

- 按组件类型分组（如 buttons、forms、modals）
- 或按功能域分组（如 user-related、data-display）

**示例**:

```
components/
├── ui/
│   ├── button/
│   ├── modal/
│   └── form-field/
└── business/
    ├── user-card/
    └── data-table/
```

### 3. services/ - 应用服务

**用途**: 存放所有业务逻辑、数据管理和 API 调用服务

**命名约定**: `service-name.service.ts`

**类型**:

- 数据服务（API 调用）
- 状态管理服务
- 工具服务

### 4. guards/ - 路由守卫

**用途**: 存放路由保护逻辑

**类型**:

- 认证守卫 (AuthGuard)
- 权限守卫 (PermissionGuard)
- 数据预加载守卫

### 5. interceptors/ - HTTP 拦截器

**用途**: 处理 HTTP 请求和响应的全局逻辑

**常见用途**:

- 添加认证头
- 错误处理
- 请求/响应日志
- 加载状态管理

### 6. models/ - 数据模型

**用途**: 定义 TypeScript 接口和类型

**组织方式**:

- 按业务域分组
- 使用 `.model.ts` 后缀

### 7. utils/ - 工具函数

**用途**: 存放纯函数和工具方法

**类型**:

- 日期处理
- 数据验证
- 格式化函数
- 常用算法

### 8. core/ - 核心模块

**用途**: 存放应用核心功能和单例服务

**内容**:

- 全局配置
- 核心服务
- 应用初始化逻辑

## 路由配置

### 懒加载策略

所有页面组件都使用懒加载，提高应用性能：

```typescript
{
  path: 'dashboard',
  loadComponent: () => import('./views/dashboard/dashboard.component').then(m => m.DashboardComponent),
  title: '仪表板'
}
```

### 路由结构

- 使用嵌套路由组织相关页面
- 为每个路由设置有意义的标题
- 实现适当的重定向策略

## 命名约定

### 文件命名

- 组件: `component-name.component.ts`
- 服务: `service-name.service.ts`
- 守卫: `guard-name.guard.ts`
- 模型: `model-name.model.ts`
- 工具: `util-name.utils.ts`

### 目录命名

- 使用 kebab-case
- 名称应该清晰描述内容
- 避免过深的嵌套（建议不超过 3 层）

### 组件选择器

- 使用应用前缀: `app-component-name`
- 遵循 kebab-case 约定

## 最佳实践

### 1. 组件设计

- 保持组件单一职责
- 使用 OnPush 变更检测策略
- 优先使用 Standalone 组件
- 合理使用 Angular Signals

### 2. 服务设计

- 使用依赖注入
- 实现适当的错误处理
- 考虑使用 RxJS 操作符

### 3. 代码组织

- 相关文件放在同一目录
- 使用 index.ts 文件简化导入
- 保持导入语句整洁

### 4. 性能优化

- 实现懒加载
- 使用 OnPush 变更检测
- 优化包大小

## 迁移指南

### 添加新页面组件

1. 在 `views/` 下创建适当的目录结构
2. 生成组件文件
3. 在 `app.routes.ts` 中添加路由配置
4. 如需要，添加相应的守卫和服务

### 添加新的可复用组件

1. 在 `components/` 下创建组件
2. 确保组件是独立的和可复用的
3. 编写完整的测试
4. 在需要的地方导入使用

### 添加新服务

1. 在 `services/` 目录下创建服务
2. 使用 `providedIn: 'root'` 或在适当的模块中提供
3. 编写单元测试
4. 在组件中注入使用

## 工具和脚本

### Angular CLI 命令

```bash
# 生成页面组件
ng generate component views/feature/page-name

# 生成可复用组件
ng generate component components/ui/component-name

# 生成服务
ng generate service services/service-name

# 生成守卫
ng generate guard guards/guard-name
```

### 代码质量

- 使用 ESLint 进行代码检查
- 使用 Prettier 进行代码格式化
- 编写单元测试和集成测试
- 使用 TypeScript 严格模式

## 总结

这个企业级目录结构提供了：

1. **清晰的关注点分离**: 页面、组件、服务各司其职
2. **良好的可扩展性**: 易于添加新功能和模块
3. **代码复用**: 共享组件和服务的合理组织
4. **维护性**: 清晰的命名和组织约定
5. **性能优化**: 懒加载和模块化设计

遵循这个结构和约定，可以确保项目在成长过程中保持整洁和可维护性。

## 相关文档

- [迁移指南](./migration-guide.md) - 如何将现有项目迁移到新结构
- [开发规范](./development-guidelines.md) - 详细的开发规范和代码标准

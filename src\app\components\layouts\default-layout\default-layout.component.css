.default-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f8fafc;
}

/* 顶部导航栏 */
.layout-header {
  height: 64px;
  background: white;
  border-bottom: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header-content {
  height: 100%;
  padding: 0 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.sidebar-toggle {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 6px;
  transition: background 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sidebar-toggle:hover {
  background: #f1f5f9;
}

.hamburger {
  display: block;
  width: 20px;
  height: 2px;
  background: #475569;
  position: relative;
  transition: all 0.3s;
}

.hamburger::before,
.hamburger::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 2px;
  background: #475569;
  transition: all 0.3s;
}

.hamburger::before {
  top: -6px;
}

.hamburger::after {
  top: 6px;
}

.sidebar-toggle.collapsed .hamburger {
  background: transparent;
}

.sidebar-toggle.collapsed .hamburger::before {
  transform: rotate(45deg);
  top: 0;
}

.sidebar-toggle.collapsed .hamburger::after {
  transform: rotate(-45deg);
  top: 0;
}

.app-title {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.mobile-menu-toggle {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 6px;
  transition: background 0.2s;
}

.mobile-menu-toggle:hover {
  background: #f1f5f9;
}

.user-info {
  position: relative;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.user-name {
  color: #64748b;
  font-weight: 500;
  font-size: 0.9rem;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  cursor: pointer;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s;
}

.user-avatar:hover {
  transform: scale(1.05);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 0.5rem;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  min-width: 160px;
  display: none;
  z-index: 1001;
  overflow: hidden;
}

.user-info:hover .dropdown-menu {
  display: block;
}

.dropdown-menu a,
.dropdown-menu button {
  display: block;
  width: 100%;
  padding: 0.75rem 1rem;
  text-decoration: none;
  color: #374151;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  transition: background 0.2s;
  font-size: 0.875rem;
}

.dropdown-menu a:hover,
.dropdown-menu button:hover {
  background: #f8fafc;
}

/* 布局主体 */
.layout-body {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* 侧边栏 */
.layout-sidebar {
  width: 280px;
  background: #1e293b;
  color: white;
  transition: width 0.3s ease;
  overflow-y: auto;
  position: relative;
}

.layout-sidebar.collapsed {
  width: 64px;
}

.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s;
}

.mobile-overlay.active {
  opacity: 1;
  visibility: visible;
}

.sidebar-nav {
  padding: 1.5rem 0;
}

.nav-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-item {
  margin-bottom: 0.25rem;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: all 0.3s;
  border-left: 3px solid transparent;
  position: relative;
}

.nav-link:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.nav-link.active {
  background: rgba(59, 130, 246, 0.2);
  border-left-color: #3b82f6;
  color: white;
}

.nav-icon {
  width: 20px;
  margin-right: 0.75rem;
  text-align: center;
  flex-shrink: 0;
}

.nav-text {
  flex: 1;
  transition: opacity 0.3s;
  white-space: nowrap;
  overflow: hidden;
}

.collapsed .nav-text {
  opacity: 0;
  width: 0;
}

.nav-group-title {
  display: flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.nav-submenu {
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-subitem .nav-link {
  padding-left: 3.5rem;
  font-size: 0.875rem;
}

/* 主内容区域 */
.layout-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: margin-left 0.3s ease;
}

.main-content {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
  background: #f8fafc;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .user-name {
    display: none;
  }
  
  .layout-sidebar {
    position: fixed;
    left: -280px;
    top: 64px;
    height: calc(100vh - 64px);
    z-index: 1000;
    transition: left 0.3s ease;
  }
  
  .layout-sidebar.mobile-open {
    left: 0;
  }
  
  .layout-main {
    margin-left: 0;
  }
  
  .main-content {
    padding: 1rem;
  }
}

@media (max-width: 640px) {
  .header-content {
    padding: 0 1rem;
  }
  
  .app-title {
    font-size: 1.25rem;
  }
  
  .main-content {
    padding: 0.75rem;
  }
}

/* 工具类 */
.md\:hidden {
  display: none;
}

@media (max-width: 768px) {
  .md\:hidden {
    display: block;
  }
}

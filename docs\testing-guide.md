# 动态路由系统测试指南

## 测试步骤

### 1. 基础功能测试

#### 访问应用首页
- 打开浏览器访问 `http://localhost:4201`
- 应该自动重定向到 `/dashboard`
- 验证仪表板页面正常显示

#### 测试登录功能
1. 访问 `http://localhost:4201/auth/login`
2. 输入任意邮箱和密码（系统会模拟登录成功）
3. 点击登录按钮
4. 观察以下行为：
   - 显示加载状态
   - 控制台输出 "登录成功，动态路由已加载"
   - 自动导航到管理员页面 `/admin`

### 2. 动态路由测试

#### 验证路由动态加载
1. 打开浏览器开发者工具
2. 切换到 Network 标签页
3. 执行登录操作
4. 观察网络请求：
   - 应该看到懒加载的组件请求
   - 管理员布局组件被加载
   - 相关的 CSS 和 JS 文件被请求

#### 测试不同布局
1. **管理员布局** (`/admin`)
   - 侧边栏导航
   - 可折叠菜单
   - 用户信息显示
   - 导航到 `/admin/dashboard` 和 `/admin/users`

2. **用户布局** (`/app`)
   - 顶部导航栏
   - 简洁界面
   - 导航到 `/app/dashboard` 和 `/app/profile`

3. **访客布局** (登录页面)
   - 最简布局
   - 渐变背景
   - 登录表单

### 3. 权限控制测试

#### 测试路由保护
1. 在未登录状态下访问受保护的路由：
   - `http://localhost:4201/admin`
   - `http://localhost:4201/app`
   - 应该重定向到登录页面

2. 登录后访问无权限页面：
   - `http://localhost:4201/unauthorized`
   - 验证无权限页面正确显示

#### 测试权限检查
1. 修改 `DynamicRouteService` 中的模拟数据
2. 移除用户的某些权限
3. 验证相应的菜单项不显示
4. 直接访问无权限路由应重定向到无权限页面

### 4. 导航菜单测试

#### 验证菜单生成
1. 登录后检查侧边栏菜单（管理员布局）
2. 验证菜单项按 `order` 字段排序
3. 检查图标和标题正确显示
4. 验证权限过滤生效

#### 测试菜单交互
1. 点击菜单项验证路由跳转
2. 测试菜单的 active 状态
3. 在管理员布局中测试侧边栏折叠功能

### 5. 状态管理测试

#### 验证用户状态
1. 登录后刷新页面
2. 验证用户状态保持（从 localStorage 恢复）
3. 验证动态路由重新加载
4. 测试登出功能清除状态

#### 测试路由状态
1. 使用 Angular DevTools 查看路由配置
2. 验证动态路由正确注册
3. 检查路由数据和权限设置

### 6. 错误处理测试

#### 测试网络错误
1. 断开网络连接
2. 尝试登录
3. 验证错误处理和用户反馈

#### 测试组件加载失败
1. 修改组件映射指向不存在的文件
2. 验证错误处理和降级方案

### 7. 性能测试

#### 验证懒加载
1. 打开 Network 标签页
2. 清除缓存
3. 访问不同页面
4. 验证组件按需加载

#### 检查包大小
1. 运行 `ng build --configuration production`
2. 检查生成的文件大小
3. 验证代码分割效果

## 预期结果

### 成功指标
- ✅ 登录后动态路由正确加载
- ✅ 不同布局正确显示
- ✅ 权限控制生效
- ✅ 导航菜单正确生成
- ✅ 路由保护正常工作
- ✅ 状态持久化正常
- ✅ 错误处理友好
- ✅ 性能表现良好

### 常见问题

#### 路由不生效
- 检查组件映射配置
- 验证路由数据格式
- 确认权限设置正确

#### 布局显示异常
- 检查 CSS 样式加载
- 验证布局组件导入
- 确认路由配置正确

#### 权限检查失败
- 验证用户权限数据
- 检查守卫配置
- 确认路由数据设置

## 调试技巧

### 使用浏览器开发者工具
1. **Console 标签页**
   - 查看错误信息和日志
   - 检查路由加载状态

2. **Network 标签页**
   - 监控组件加载
   - 检查 API 请求

3. **Application 标签页**
   - 查看 localStorage 数据
   - 检查用户状态

### 使用 Angular DevTools
1. 安装 Angular DevTools 扩展
2. 查看组件树和状态
3. 监控路由变化
4. 调试 Signals 状态

### 代码调试
1. 在关键方法中添加 `console.log`
2. 使用断点调试
3. 检查网络请求和响应
4. 验证数据流转

## 自动化测试

### 单元测试
```typescript
// 示例：测试 DynamicRouteService
describe('DynamicRouteService', () => {
  it('should load user routes', () => {
    // 测试路由加载
  });
  
  it('should apply dynamic routes', () => {
    // 测试路由应用
  });
});
```

### 集成测试
```typescript
// 示例：测试登录流程
describe('Login Flow', () => {
  it('should redirect to admin after login', () => {
    // 测试登录后重定向
  });
});
```

### E2E 测试
```typescript
// 示例：端到端测试
describe('Dynamic Routing E2E', () => {
  it('should navigate through dynamic routes', () => {
    // 测试完整用户流程
  });
});
```

通过这些测试步骤，您可以全面验证动态路由系统的功能和性能。

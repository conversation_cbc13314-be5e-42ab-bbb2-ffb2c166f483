import { Component, signal, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';

interface DashboardCard {
  title: string;
  value: string;
  icon: string;
  trend: 'up' | 'down' | 'neutral';
  trendValue: string;
}

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './dashboard.component.html',
  styleUrl: './dashboard.component.css'
})
export class DashboardComponent implements OnInit {
  protected readonly isLoading = signal(true);
  protected readonly cards = signal<DashboardCard[]>([]);
  protected readonly currentUser = signal('用户');

  constructor(private router: Router) {}

  ngOnInit(): void {
    this.loadDashboardData();
  }

  private loadDashboardData(): void {
    // 模拟数据加载
    setTimeout(() => {
      this.cards.set([
        {
          title: '总用户数',
          value: '12,345',
          icon: '👥',
          trend: 'up',
          trendValue: '+12%'
        },
        {
          title: '月活跃用户',
          value: '8,901',
          icon: '📊',
          trend: 'up',
          trendValue: '+8%'
        },
        {
          title: '总收入',
          value: '¥234,567',
          icon: '💰',
          trend: 'down',
          trendValue: '-3%'
        },
        {
          title: '订单数量',
          value: '1,234',
          icon: '📦',
          trend: 'neutral',
          trendValue: '0%'
        }
      ]);
      this.isLoading.set(false);
    }, 1000);
  }

  navigateToProfile(): void {
    this.router.navigate(['/user/profile']);
  }

  navigateToSettings(): void {
    this.router.navigate(['/user/settings']);
  }

  logout(): void {
    this.router.navigate(['/auth/login']);
  }
}

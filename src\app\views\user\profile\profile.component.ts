import { Component, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';

interface UserProfile {
  name: string;
  email: string;
  phone: string;
  department: string;
  position: string;
  joinDate: string;
}

@Component({
  selector: 'app-profile',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './profile.component.html',
  styleUrl: './profile.component.css'
})
export class ProfileComponent {
  protected readonly isEditing = signal(false);
  protected readonly isSaving = signal(false);
  protected readonly profile = signal<UserProfile>({
    name: '张三',
    email: '<EMAIL>',
    phone: '13800138000',
    department: '技术部',
    position: '高级开发工程师',
    joinDate: '2023-01-15'
  });

  constructor(private router: Router) {}

  toggleEdit(): void {
    this.isEditing.set(!this.isEditing());
  }

  saveProfile(): void {
    this.isSaving.set(true);
    
    // 模拟保存请求
    setTimeout(() => {
      this.isSaving.set(false);
      this.isEditing.set(false);
      // 这里可以显示成功消息
    }, 1000);
  }

  cancelEdit(): void {
    this.isEditing.set(false);
    // 这里可以重置表单数据
  }

  goBack(): void {
    this.router.navigate(['/dashboard']);
  }

  updateProfile(field: keyof UserProfile, value: string): void {
    this.profile.update(current => ({
      ...current,
      [field]: value
    }));
  }
}

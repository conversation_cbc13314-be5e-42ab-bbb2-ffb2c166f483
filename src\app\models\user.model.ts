export interface User {
  id: string;
  name: string;
  email: string;
  phone: string;
  department: string;
  position: string;
  joinDate: string;
  avatar?: string;
  isActive?: boolean;
  lastLoginDate?: string;
}

export interface UserProfile extends Omit<User, 'id' | 'isActive' | 'lastLoginDate'> {
  // 用户资料特定的字段可以在这里添加
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  user: User;
  token: string;
  refreshToken: string;
}

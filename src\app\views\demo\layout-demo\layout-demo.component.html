<div class="layout-demo-container">
  <div class="demo-header">
    <h1>布局系统演示</h1>
    <p class="demo-description">
      本项目采用基于页面类型的布局系统，根据页面功能和展示需求选择合适的布局。
      点击下方的演示链接体验不同布局的效果。
    </p>
  </div>

  <div class="layouts-grid">
    @for (layout of layouts; track layout.name) {
    <div class="layout-card">
      <div class="card-header">
        <h3 class="layout-name">{{ layout.name }}</h3>
        <span class="layout-type">{{ layout.layoutType }}</span>
      </div>

      <p class="layout-description">{{ layout.description }}</p>

      <div class="features-section">
        <h4>主要特点</h4>
        <ul class="features-list">
          @for (feature of layout.features; track feature) {
          <li>{{ feature }}</li>
          }
        </ul>
      </div>

      <div class="use-case-section">
        <h4>适用场景</h4>
        <p class="use-case">{{ layout.useCase }}</p>
      </div>

      <div class="card-actions">
        <a [routerLink]="layout.demoPath" class="demo-button" target="_blank"> 查看演示 </a>
        <button class="code-button" (click)="showCode(layout.name)">查看代码</button>
      </div>
    </div>
    }
  </div>

  <div class="comparison-section">
    <h2>布局对比</h2>
    <div class="comparison-table">
      <table>
        <thead>
          <tr>
            <th>布局类型</th>
            <th>导航方式</th>
            <th>复杂度</th>
            <th>适用场景</th>
            <th>移动端</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td><strong>DefaultLayout</strong></td>
            <td>顶部 + 侧边栏</td>
            <td>高</td>
            <td>管理后台、复杂应用</td>
            <td>✅ 响应式</td>
          </tr>
          <tr>
            <td><strong>TopNavLayout</strong></td>
            <td>顶部水平导航</td>
            <td>中</td>
            <td>内容展示、简洁应用</td>
            <td>✅ 响应式</td>
          </tr>
          <tr>
            <td><strong>BlankLayout</strong></td>
            <td>无导航</td>
            <td>低</td>
            <td>全屏页面、特殊功能</td>
            <td>✅ 全屏</td>
          </tr>
          <tr>
            <td><strong>GuestLayout</strong></td>
            <td>简单导航</td>
            <td>低</td>
            <td>登录注册、公开页面</td>
            <td>✅ 响应式</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>

  <div class="usage-guide">
    <h2>使用指南</h2>
    <div class="guide-content">
      <div class="guide-item">
        <h3>1. 选择合适的布局</h3>
        <p>根据页面的功能复杂度和用户交互需求选择布局类型。</p>
      </div>

      <div class="guide-item">
        <h3>2. 配置路由</h3>
        <p>在动态路由配置中指定 layout 字段来使用相应的布局。</p>
        <pre><code>{{ '{' }}
  "id": "my-page",
  "path": "my-page",
  "title": "我的页面",
  "component": "MyComponent",
  "layout": "DefaultLayout"
{{ '}' }}</code></pre>
      </div>

      <div class="guide-item">
        <h3>3. 权限控制</h3>
        <p>布局系统与权限控制完全兼容，可以为不同布局的页面设置不同权限。</p>
      </div>
    </div>
  </div>
</div>

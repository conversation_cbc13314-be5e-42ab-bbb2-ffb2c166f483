.unauthorized-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
  padding: 2rem;
}

.unauthorized-content {
  max-width: 500px;
  width: 100%;
  text-align: center;
  background: white;
  padding: 3rem 2rem;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.error-icon {
  margin-bottom: 2rem;
  display: flex;
  justify-content: center;
}

.error-icon svg {
  opacity: 0.8;
}

.error-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 1rem 0;
}

.error-message {
  font-size: 1.125rem;
  color: #4a5568;
  margin: 0 0 2rem 0;
  line-height: 1.6;
}

.user-info {
  background: #f7fafc;
  padding: 1.5rem;
  border-radius: 8px;
  margin-bottom: 2rem;
  border-left: 4px solid #3182ce;
}

.user-info p {
  margin: 0.5rem 0;
  color: #2d3748;
  font-size: 0.9rem;
}

.user-info strong {
  color: #1a202c;
  font-weight: 600;
}

.action-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
  font-size: 0.9rem;
}

.btn-primary {
  background: #3182ce;
  color: white;
}

.btn-primary:hover {
  background: #2c5aa0;
  transform: translateY(-1px);
}

.btn-outline {
  background: transparent;
  border: 2px solid #e2e8f0;
  color: #4a5568;
}

.btn-outline:hover {
  background: #f7fafc;
  border-color: #cbd5e0;
  transform: translateY(-1px);
}

.help-text {
  padding-top: 1.5rem;
  border-top: 1px solid #e2e8f0;
}

.help-text p {
  margin: 0;
  color: #718096;
  font-size: 0.875rem;
  font-style: italic;
}

@media (max-width: 640px) {
  .unauthorized-container {
    padding: 1rem;
  }
  
  .unauthorized-content {
    padding: 2rem 1.5rem;
  }
  
  .error-title {
    font-size: 2rem;
  }
  
  .action-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .btn {
    width: 100%;
    max-width: 200px;
    justify-content: center;
  }
}

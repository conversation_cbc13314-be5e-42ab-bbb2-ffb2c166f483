# 动态角色系统测试指南

## 🚀 快速开始

应用已启动在：**http://localhost:4204**

## 🧪 测试场景

### 1. 系统管理员登录测试

**测试账号**：
- 邮箱：`<EMAIL>`
- 密码：`admin123`
- 角色：`["admin", "manager"]`

**预期结果**：
- 使用 DefaultLayout（完整导航和侧边栏）
- 导航到 `/admin` 路由
- 显示管理仪表板和系统设置功能
- 拥有最高权限

**测试步骤**：
1. 访问 http://localhost:4204
2. 点击"系统管理员"演示账号卡片
3. 点击"登录"按钮
4. 验证界面布局和功能

### 2. 部门经理登录测试

**测试账号**：
- 邮箱：`<EMAIL>`
- 密码：`manager123`
- 角色：`["manager", "editor"]`

**预期结果**：
- 使用 DefaultLayout（管理界面）
- 导航到 `/management` 路由
- 显示团队管理和内容编辑功能
- 拥有管理和编辑权限

### 3. 普通用户登录测试

**测试账号**：
- 邮箱：`<EMAIL>`
- 密码：`user123`
- 角色：`["user"]`

**预期结果**：
- 使用 TopNavLayout（简洁顶部导航）
- 导航到 `/app` 路由
- 显示个人仪表板和基本功能
- 拥有基本用户权限

### 4. 访客用户登录测试

**测试账号**：
- 邮箱：`<EMAIL>`
- 密码：`guest123`
- 角色：`["guest"]`

**预期结果**：
- 使用 TopNavLayout（简洁界面）
- 导航到 `/guest` 路由
- 显示受限的浏览功能
- 拥有最低权限

## 🔍 关键测试点

### 1. 动态角色识别
- ✅ 系统能根据后端返回的角色自动确定用户类型
- ✅ 多角色用户能正确选择主要角色（按优先级）
- ✅ 未知角色能正确处理（使用默认配置）

### 2. 布局自动适配
- ✅ 管理员和经理使用 DefaultLayout（完整功能）
- ✅ 普通用户和访客使用 TopNavLayout（简洁界面）
- ✅ 布局切换流畅，无闪烁或错误

### 3. 权限控制
- ✅ 不同角色只能访问被授权的页面
- ✅ 权限验证在路由级别生效
- ✅ 无权限访问时正确跳转到未授权页面

### 4. 用户体验
- ✅ 登录流程简洁，无需选择账号类型
- ✅ 演示账号一键登录功能正常
- ✅ 错误消息清晰友好
- ✅ 登录成功后自动导航到相应页面

## 🐛 常见问题排查

### 问题1：登录后页面空白
**可能原因**：路由配置错误或组件加载失败
**解决方法**：
1. 检查浏览器控制台错误信息
2. 验证路由配置是否正确
3. 确认组件文件存在且可访问

### 问题2：权限验证失败
**可能原因**：角色映射配置错误
**解决方法**：
1. 检查 `AccountTypeService.getRoleMappingConfig()` 返回值
2. 验证用户角色是否在映射配置中
3. 确认权限聚合逻辑正确

### 问题3：布局显示异常
**可能原因**：布局组件加载错误或样式冲突
**解决方法**：
1. 检查布局组件是否正确导入
2. 验证 CSS 样式是否冲突
3. 确认布局选择逻辑正确

## 📊 测试检查清单

### 基础功能测试
- [ ] 所有演示账号都能正常登录
- [ ] 登录后正确导航到对应页面
- [ ] 不同角色显示不同的界面布局
- [ ] 权限控制正确工作

### 错误处理测试
- [ ] 错误的邮箱/密码显示错误消息
- [ ] 网络错误时有友好提示
- [ ] 未授权访问正确处理

### 用户体验测试
- [ ] 界面响应速度快
- [ ] 布局切换流畅
- [ ] 移动端适配良好
- [ ] 无明显的用户体验问题

### 兼容性测试
- [ ] 现有功能继续正常工作
- [ ] 旧的路由配置仍然有效
- [ ] 向后兼容性良好

## 🔧 调试技巧

### 1. 查看用户信息
在浏览器控制台执行：
```javascript
// 查看当前用户信息
console.log(JSON.parse(localStorage.getItem('currentUser')));

// 查看角色映射配置
console.log(JSON.parse(localStorage.getItem('roleMappingCache')));
```

### 2. 查看路由状态
```javascript
// 查看当前路由配置
console.log(window.location.pathname);

// 查看动态路由状态
console.log('Dynamic routes loaded');
```

### 3. 模拟不同角色
可以通过修改 `UserService.getMockUsers()` 方法来测试不同的角色组合：
```typescript
// 添加自定义测试用户
{
  id: 'test_user',
  name: '测试用户',
  email: '<EMAIL>',
  roles: ['custom-role'],  // 测试自定义角色
  permissions: ['custom-permission']
}
```

## 📈 性能监控

### 关键指标
- **登录响应时间**：应在 500ms 内完成
- **路由加载时间**：应在 200ms 内完成
- **布局切换时间**：应在 100ms 内完成
- **内存使用**：无明显内存泄漏

### 监控方法
1. 使用浏览器开发者工具的 Performance 面板
2. 监控 Network 面板的请求时间
3. 观察 Console 面板的错误和警告

## 🎯 下一步测试

1. **集成测试**：与真实后端 API 集成测试
2. **压力测试**：模拟大量用户同时登录
3. **安全测试**：验证权限控制的安全性
4. **兼容性测试**：在不同浏览器和设备上测试

恭喜！您的动态角色系统已经成功实现并可以开始测试了！🎉

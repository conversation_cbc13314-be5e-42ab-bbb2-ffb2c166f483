# 项目结构迁移指南

## 概述

本指南帮助您将现有的 Angular 项目迁移到新的企业级目录结构。迁移过程分为几个阶段，可以逐步进行，不会影响现有功能。

## 迁移前准备

### 1. 备份项目
```bash
# 创建项目备份
git checkout -b backup-before-migration
git commit -am "Backup before structure migration"

# 创建新的迁移分支
git checkout -b feature/enterprise-structure-migration
```

### 2. 分析现有结构
- 列出所有现有组件
- 识别页面级组件和可复用组件
- 检查现有服务和工具函数
- 记录当前路由配置

## 迁移步骤

### 第一阶段：创建新目录结构

```bash
# 创建新的目录结构
mkdir -p src/app/views/{auth,dashboard,user,admin,shared}
mkdir -p src/app/components
mkdir -p src/app/services
mkdir -p src/app/guards
mkdir -p src/app/interceptors
mkdir -p src/app/models
mkdir -p src/app/utils
mkdir -p src/app/core
```

### 第二阶段：迁移页面组件

#### 识别页面组件
页面组件通常具有以下特征：
- 在路由配置中直接使用
- 包含完整的页面布局
- 不被其他组件复用

#### 迁移步骤
1. **移动文件**
   ```bash
   # 示例：移动登录组件
   mv src/app/login src/app/views/auth/login
   ```

2. **更新导入路径**
   ```typescript
   // 更新路由配置
   // 从：
   loadComponent: () => import('./login/login.component').then(m => m.LoginComponent)
   
   // 到：
   loadComponent: () => import('./views/auth/login/login.component').then(m => m.LoginComponent)
   ```

3. **更新相对导入**
   检查组件内部的相对导入并更新路径

#### 按模块迁移页面组件

**认证模块**
```bash
# 移动认证相关组件
mv src/app/login src/app/views/auth/login
mv src/app/register src/app/views/auth/register
mv src/app/forgot-password src/app/views/auth/forgot-password
```

**用户模块**
```bash
# 移动用户相关组件
mv src/app/profile src/app/views/user/profile
mv src/app/settings src/app/views/user/settings
```

**管理模块**
```bash
# 移动管理相关组件
mv src/app/admin-users src/app/views/admin/users
mv src/app/admin-settings src/app/views/admin/settings
```

### 第三阶段：迁移可复用组件

#### 识别可复用组件
- 被多个页面使用的组件
- UI 组件（按钮、表单、模态框等）
- 业务组件（用户卡片、数据表格等）

#### 迁移步骤
```bash
# 移动可复用组件到 components 目录
mv src/app/shared/button src/app/components/ui/button
mv src/app/shared/modal src/app/components/ui/modal
mv src/app/shared/user-card src/app/components/business/user-card
```

### 第四阶段：迁移服务

```bash
# 移动服务文件
mv src/app/auth.service.ts src/app/services/auth.service.ts
mv src/app/user.service.ts src/app/services/user.service.ts
mv src/app/api.service.ts src/app/services/api.service.ts
```

### 第五阶段：迁移其他文件

#### 守卫
```bash
mv src/app/auth.guard.ts src/app/guards/auth.guard.ts
```

#### 拦截器
```bash
mv src/app/auth.interceptor.ts src/app/interceptors/auth.interceptor.ts
```

#### 模型和接口
```bash
mv src/app/user.interface.ts src/app/models/user.model.ts
mv src/app/api.interface.ts src/app/models/api.model.ts
```

#### 工具函数
```bash
mv src/app/utils.ts src/app/utils/common.utils.ts
```

### 第六阶段：更新导入路径

#### 使用 IDE 重构工具
大多数现代 IDE 提供自动重构功能：
1. 右键点击移动的文件/目录
2. 选择 "Refactor" > "Move"
3. IDE 会自动更新所有导入路径

#### 手动更新（如果需要）
```typescript
// 更新组件导入
// 从：
import { UserService } from '../user.service';

// 到：
import { UserService } from '../../services/user.service';
```

#### 批量查找替换
使用 VS Code 的全局查找替换功能：
1. 按 `Ctrl+Shift+H` 打开替换面板
2. 使用正则表达式模式
3. 批量替换导入路径

### 第七阶段：更新路由配置

```typescript
// app.routes.ts
export const routes: Routes = [
  {
    path: 'auth',
    children: [
      {
        path: 'login',
        loadComponent: () => import('./views/auth/login/login.component').then(m => m.LoginComponent)
      }
      // ... 其他认证路由
    ]
  },
  {
    path: 'dashboard',
    loadComponent: () => import('./views/dashboard/dashboard.component').then(m => m.DashboardComponent)
  }
  // ... 其他路由
];
```

## 迁移检查清单

### 文件迁移
- [ ] 所有页面组件已移动到 `views/` 目录
- [ ] 可复用组件已移动到 `components/` 目录
- [ ] 服务已移动到 `services/` 目录
- [ ] 守卫已移动到 `guards/` 目录
- [ ] 拦截器已移动到 `interceptors/` 目录
- [ ] 模型已移动到 `models/` 目录
- [ ] 工具函数已移动到 `utils/` 目录

### 代码更新
- [ ] 所有导入路径已更新
- [ ] 路由配置已更新
- [ ] 相对路径已修正
- [ ] 测试文件路径已更新

### 功能验证
- [ ] 应用可以正常启动
- [ ] 所有路由可以正常访问
- [ ] 组件功能正常
- [ ] 服务注入正常
- [ ] 测试可以正常运行

## 常见问题和解决方案

### 1. 导入路径错误
**问题**: 移动文件后出现导入错误
**解决**: 使用 IDE 的自动重构功能或手动更新路径

### 2. 循环依赖
**问题**: 重组后出现循环依赖警告
**解决**: 检查服务和组件之间的依赖关系，使用接口解耦

### 3. 测试失败
**问题**: 测试文件找不到依赖
**解决**: 更新测试文件中的导入路径

### 4. 懒加载失败
**问题**: 路由懒加载无法正常工作
**解决**: 检查 `loadComponent` 中的路径是否正确

## 迁移后优化

### 1. 添加 index.ts 文件
在主要目录下创建 `index.ts` 文件简化导入：

```typescript
// src/app/services/index.ts
export * from './user.service';
export * from './auth.service';
export * from './api.service';
```

### 2. 配置路径映射
在 `tsconfig.json` 中添加路径映射：

```json
{
  "compilerOptions": {
    "paths": {
      "@app/*": ["src/app/*"],
      "@views/*": ["src/app/views/*"],
      "@components/*": ["src/app/components/*"],
      "@services/*": ["src/app/services/*"]
    }
  }
}
```

### 3. 更新 ESLint 规则
配置 ESLint 规则以强制使用新的目录结构。

## 验证迁移成功

### 1. 构建测试
```bash
ng build --configuration production
```

### 2. 单元测试
```bash
ng test
```

### 3. 端到端测试
```bash
ng e2e
```

### 4. 功能测试
手动测试所有主要功能确保正常工作。

## 总结

迁移到新的企业级目录结构需要仔细规划和执行。建议：

1. 分阶段进行迁移
2. 充分利用 IDE 的重构工具
3. 在每个阶段后进行测试
4. 保持代码备份
5. 团队成员之间保持沟通

完成迁移后，您的项目将具有更好的可维护性、可扩展性和团队协作效率。

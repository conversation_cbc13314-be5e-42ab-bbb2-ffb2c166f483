.top-nav-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: #f8fafc;
}

/* 顶部导航栏 */
.top-nav-header {
  background: white;
  border-bottom: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
}

.header-left {
  display: flex;
  align-items: center;
}

.app-title {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
}

.app-title a {
  color: #1e293b;
  text-decoration: none;
  transition: color 0.2s;
}

.app-title a:hover {
  color: #3b82f6;
}

/* 主导航 */
.main-nav {
  flex: 1;
  display: flex;
  justify-content: center;
  margin: 0 2rem;
}

.nav-list {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 0.5rem;
}

.nav-item {
  position: relative;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  color: #64748b;
  text-decoration: none;
  border-radius: 6px;
  transition: all 0.2s;
  font-weight: 500;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 0.9rem;
}

.nav-link:hover {
  background: #f1f5f9;
  color: #1e293b;
}

.nav-link.active {
  background: #dbeafe;
  color: #1d4ed8;
}

.nav-icon {
  font-size: 1rem;
}

.dropdown-arrow {
  font-size: 0.75rem;
  margin-left: 0.25rem;
  transition: transform 0.2s;
}

/* 下拉菜单 */
.dropdown:hover .dropdown-arrow {
  transform: rotate(180deg);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  min-width: 200px;
  display: none;
  z-index: 1001;
  overflow: hidden;
  list-style: none;
  margin: 0.5rem 0 0 0;
  padding: 0.5rem 0;
}

.dropdown:hover .dropdown-menu {
  display: block;
}

.dropdown-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  color: #374151;
  text-decoration: none;
  transition: background 0.2s;
  font-size: 0.875rem;
}

.dropdown-link:hover {
  background: #f8fafc;
  color: #1e293b;
}

.dropdown-link.active {
  background: #dbeafe;
  color: #1d4ed8;
}

/* 用户信息 */
.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  position: relative;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.user-name {
  color: #64748b;
  font-weight: 500;
  font-size: 0.9rem;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  cursor: pointer;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s;
}

.user-avatar:hover {
  transform: scale(1.05);
}

.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 0.5rem;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  min-width: 160px;
  display: none;
  z-index: 1001;
  overflow: hidden;
}

.user-info:hover .user-dropdown {
  display: block;
}

.user-dropdown a,
.user-dropdown button {
  display: block;
  width: 100%;
  padding: 0.75rem 1rem;
  text-decoration: none;
  color: #374151;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  transition: background 0.2s;
  font-size: 0.875rem;
}

.user-dropdown a:hover,
.user-dropdown button:hover {
  background: #f8fafc;
}

/* 主内容区域 */
.top-nav-main {
  flex: 1;
  padding: 2rem 0;
}

.main-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* 底部 */
.top-nav-footer {
  background: white;
  border-top: 1px solid #e2e8f0;
  padding: 1.5rem 0;
  margin-top: auto;
}

.footer-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  text-align: center;
}

.footer-container p {
  margin: 0;
  color: #64748b;
  font-size: 0.875rem;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .header-container {
    padding: 0 1.5rem;
  }
  
  .main-nav {
    margin: 0 1rem;
  }
  
  .main-container {
    padding: 0 1.5rem;
  }
  
  .footer-container {
    padding: 0 1.5rem;
  }
}

@media (max-width: 768px) {
  .header-container {
    flex-direction: column;
    height: auto;
    padding: 1rem;
    gap: 1rem;
  }
  
  .main-nav {
    order: 3;
    width: 100%;
    margin: 0;
  }
  
  .nav-list {
    justify-content: center;
    flex-wrap: wrap;
    gap: 0.25rem;
  }
  
  .nav-link {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
  }
  
  .header-right {
    order: 2;
  }
  
  .user-name {
    display: none;
  }
  
  .main-container {
    padding: 0 1rem;
  }
  
  .top-nav-main {
    padding: 1rem 0;
  }
}

@media (max-width: 640px) {
  .header-container {
    padding: 0.75rem;
  }
  
  .app-title {
    font-size: 1.25rem;
  }
  
  .nav-list {
    gap: 0.125rem;
  }
  
  .nav-link {
    padding: 0.5rem;
    font-size: 0.8rem;
  }
  
  .main-container {
    padding: 0 0.75rem;
  }
}

import { Injectable } from '@angular/core';
import { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  intercept(req: HttpRequest<any>, next: <PERSON>ttpHandler): Observable<HttpEvent<any>> {
    // 从本地存储或服务中获取认证令牌
    const authToken = localStorage.getItem('auth_token');
    
    if (authToken) {
      // 克隆请求并添加认证头
      const authReq = req.clone({
        headers: req.headers.set('Authorization', `Bearer ${authToken}`)
      });
      
      return next.handle(authReq);
    }
    
    return next.handle(req);
  }
}

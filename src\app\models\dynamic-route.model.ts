/**
 * 动态路由相关的类型定义
 */

export interface DynamicRouteConfig {
  id: string;
  path: string;
  title: string;
  component: string;
  layout?: string;
  icon?: string;
  order?: number;
  permissions?: string[];
  children?: DynamicRouteConfig[];
  data?: Record<string, any>;
  canActivate?: string[];
  loadChildren?: string;
  redirectTo?: string;
  pathMatch?: 'full' | 'prefix';
}

export interface UserRouteResponse {
  routes: DynamicRouteConfig[];
  layouts: LayoutConfig[];
  permissions: string[];
  user: {
    id: string;
    name: string;
    role: string;
    permissions: string[];
  };
}

export interface LayoutConfig {
  name: string;
  component: string;
  description?: string;
}

export interface ComponentMapping {
  [key: string]: () => Promise<any>;
}

export interface LayoutMapping {
  [key: string]: () => Promise<any>;
}

export interface RoutePermission {
  routeId: string;
  permissions: string[];
  roles: string[];
}

export interface NavigationItem {
  id: string;
  title: string;
  path: string;
  icon?: string;
  order: number;
  children?: NavigationItem[];
  permissions?: string[];
}

export type RouteLoadingState = 'idle' | 'loading' | 'loaded' | 'error';

export interface DynamicRouteState {
  routes: DynamicRouteConfig[];
  loadingState: RouteLoadingState;
  error: string | null;
  lastUpdated: Date | null;
}

// ============ 动态角色系统接口 ============

// 角色配置接口
export interface RoleConfig {
  accountType: string;        // 映射到的账号类型
  defaultLayout: string;      // 默认布局
  defaultRoute: string;       // 默认路由
  layoutPriority: number;     // 优先级，数字越小优先级越高
  permissions?: string[];     // 角色权限（可选，主要从用户权限获取）
}

// 角色映射响应接口
export interface RoleMappingResponse {
  roleMapping: Record<string, RoleConfig>;
  layoutConfig: Record<string, {
    name: string;
    description: string;
  }>;
}

// 用户信息接口（动态角色版本）
export interface UserInfo {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  roles: string[];           // 角色列表，如 ["manager", "editor"]
  permissions: string[];     // 权限列表
  profile?: {
    department?: string;
    position?: string;
    phone?: string;
    joinDate?: string;
  };
}

// 登录凭据（简化版本）
export interface LoginCredentials {
  email: string;
  password: string;
}

// 登录响应（动态角色版本）
export interface LoginResponse {
  success: boolean;
  user: UserInfo;
  token: string;
  expiresIn: number;
  message?: string;
}

// ============ 兼容性接口（保留用于向后兼容）============

// 用户账号类型定义（已弃用，保留用于兼容）
export type UserAccountType = 'admin' | 'user' | 'guest';

export interface UserAccountTypeConfig {
  type: UserAccountType;
  name: string;
  description: string;
  defaultLayout: string;
  defaultRoute: string;
  permissions: string[];
  features: string[];
}

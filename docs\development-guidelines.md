# Angular 开发规范指南

## 概述

本文档定义了基于新企业级目录结构的 Angular 开发规范，确保代码质量、一致性和可维护性。

## 代码风格

### 1. TypeScript 规范

#### 变量和函数命名
```typescript
// ✅ 好的命名
const userProfile = signal<UserProfile | null>(null);
const isLoading = signal(false);

function getUserById(id: string): Observable<User> { }
function validateEmailFormat(email: string): boolean { }

// ❌ 避免的命名
const data = signal(null);
const flag = signal(false);
function get(id: string) { }
function check(email: string) { }
```

#### 类型定义
```typescript
// ✅ 使用接口定义对象类型
interface UserProfile {
  readonly id: string;
  name: string;
  email: string;
  department: string;
}

// ✅ 使用联合类型
type LoadingState = 'idle' | 'loading' | 'success' | 'error';

// ✅ 使用泛型
interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
}
```

### 2. Angular 组件规范

#### 组件结构
```typescript
@Component({
  selector: 'app-user-profile',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './user-profile.component.html',
  styleUrl: './user-profile.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class UserProfileComponent implements OnInit, OnDestroy {
  // 1. 信号和状态
  protected readonly user = signal<User | null>(null);
  protected readonly isLoading = signal(false);
  
  // 2. 依赖注入
  constructor(
    private userService: UserService,
    private router: Router
  ) {}
  
  // 3. 生命周期钩子
  ngOnInit(): void {
    this.loadUserProfile();
  }
  
  ngOnDestroy(): void {
    // 清理逻辑
  }
  
  // 4. 公共方法
  saveProfile(): void {
    // 实现逻辑
  }
  
  // 5. 私有方法
  private loadUserProfile(): void {
    // 实现逻辑
  }
}
```

#### 模板规范
```html
<!-- ✅ 使用新的控制流语法 -->
@if (isLoading()) {
  <div class="loading-spinner">加载中...</div>
} @else if (user()) {
  <div class="user-profile">
    <h1>{{ user()!.name }}</h1>
    <p>{{ user()!.email }}</p>
  </div>
} @else {
  <div class="error-message">用户信息加载失败</div>
}

<!-- ✅ 使用 @for 循环 -->
@for (item of items(); track item.id) {
  <div class="item">{{ item.name }}</div>
}

<!-- ✅ 使用模板引用变量 -->
<input #emailInput 
       type="email" 
       (input)="updateEmail(emailInput.value)">
```

### 3. 服务规范

```typescript
@Injectable({
  providedIn: 'root'
})
export class UserService {
  private readonly apiUrl = '/api/users';
  private readonly currentUser = signal<User | null>(null);
  
  constructor(private http: HttpClient) {}
  
  // ✅ 返回 Observable
  getUser(id: string): Observable<User> {
    return this.http.get<User>(`${this.apiUrl}/${id}`).pipe(
      catchError(this.handleError)
    );
  }
  
  // ✅ 使用信号管理状态
  getCurrentUser(): User | null {
    return this.currentUser();
  }
  
  setCurrentUser(user: User): void {
    this.currentUser.set(user);
  }
  
  // ✅ 错误处理
  private handleError(error: HttpErrorResponse): Observable<never> {
    console.error('API Error:', error);
    return throwError(() => new Error('操作失败，请稍后重试'));
  }
}
```

## 目录和文件组织

### 1. 组件组织

#### 页面组件 (views/)
```
views/user/profile/
├── profile.component.ts       # 组件逻辑
├── profile.component.html     # 模板
├── profile.component.css      # 样式
├── profile.component.spec.ts  # 测试
└── index.ts                   # 导出文件（可选）
```

#### 可复用组件 (components/)
```
components/ui/button/
├── button.component.ts
├── button.component.html
├── button.component.css
├── button.component.spec.ts
└── button.types.ts           # 组件特定类型
```

### 2. 服务组织

```
services/
├── api/                      # API 相关服务
│   ├── user-api.service.ts
│   └── auth-api.service.ts
├── state/                    # 状态管理服务
│   ├── user-state.service.ts
│   └── app-state.service.ts
└── utils/                    # 工具服务
    ├── storage.service.ts
    └── notification.service.ts
```

## 状态管理

### 1. 使用 Angular Signals

```typescript
// ✅ 状态管理服务
@Injectable({
  providedIn: 'root'
})
export class UserStateService {
  // 私有状态
  private readonly _users = signal<User[]>([]);
  private readonly _loading = signal(false);
  private readonly _error = signal<string | null>(null);
  
  // 公共只读状态
  readonly users = this._users.asReadonly();
  readonly loading = this._loading.asReadonly();
  readonly error = this._error.asReadonly();
  
  // 计算属性
  readonly userCount = computed(() => this._users().length);
  readonly hasUsers = computed(() => this._users().length > 0);
  
  // 状态更新方法
  setUsers(users: User[]): void {
    this._users.set(users);
  }
  
  addUser(user: User): void {
    this._users.update(users => [...users, user]);
  }
  
  setLoading(loading: boolean): void {
    this._loading.set(loading);
  }
}
```

### 2. 组件中使用状态

```typescript
@Component({
  // ...
})
export class UserListComponent {
  // 注入状态服务
  constructor(private userState: UserStateService) {}
  
  // 直接使用状态
  protected readonly users = this.userState.users;
  protected readonly loading = this.userState.loading;
  protected readonly userCount = this.userState.userCount;
}
```

## 错误处理

### 1. 全局错误处理

```typescript
// interceptors/error.interceptor.ts
@Injectable()
export class ErrorInterceptor implements HttpInterceptor {
  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    return next.handle(req).pipe(
      catchError((error: HttpErrorResponse) => {
        if (error.status === 401) {
          // 处理认证错误
          this.router.navigate(['/auth/login']);
        } else if (error.status >= 500) {
          // 处理服务器错误
          this.notificationService.showError('服务器错误，请稍后重试');
        }
        
        return throwError(() => error);
      })
    );
  }
}
```

### 2. 组件级错误处理

```typescript
@Component({
  // ...
})
export class UserProfileComponent {
  protected readonly error = signal<string | null>(null);
  
  loadUser(id: string): void {
    this.userService.getUser(id).pipe(
      catchError(error => {
        this.error.set('加载用户信息失败');
        return EMPTY;
      })
    ).subscribe(user => {
      this.user.set(user);
    });
  }
}
```

## 性能优化

### 1. 变更检测优化

```typescript
@Component({
  // ✅ 使用 OnPush 策略
  changeDetection: ChangeDetectionStrategy.OnPush,
  // ...
})
export class OptimizedComponent {
  // ✅ 使用信号避免不必要的变更检测
  protected readonly data = signal<Data[]>([]);
  
  // ✅ 使用 computed 进行派生状态
  protected readonly filteredData = computed(() => 
    this.data().filter(item => item.active)
  );
}
```

### 2. 懒加载

```typescript
// ✅ 路由懒加载
{
  path: 'admin',
  loadComponent: () => import('./views/admin/admin.component').then(m => m.AdminComponent)
}

// ✅ 组件懒加载
@Component({
  template: `
    @defer (when shouldLoadChart) {
      <app-chart [data]="chartData()"></app-chart>
    } @placeholder {
      <div>图表加载中...</div>
    }
  `
})
export class DashboardComponent {
  protected readonly shouldLoadChart = signal(false);
}
```

## 测试规范

### 1. 组件测试

```typescript
describe('UserProfileComponent', () => {
  let component: UserProfileComponent;
  let fixture: ComponentFixture<UserProfileComponent>;
  let mockUserService: jasmine.SpyObj<UserService>;

  beforeEach(async () => {
    const spy = jasmine.createSpyObj('UserService', ['getUser', 'updateUser']);

    await TestBed.configureTestingModule({
      imports: [UserProfileComponent],
      providers: [
        { provide: UserService, useValue: spy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(UserProfileComponent);
    component = fixture.componentInstance;
    mockUserService = TestBed.inject(UserService) as jasmine.SpyObj<UserService>;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load user on init', () => {
    const mockUser: User = { id: '1', name: 'Test User', email: '<EMAIL>' };
    mockUserService.getUser.and.returnValue(of(mockUser));

    component.ngOnInit();

    expect(mockUserService.getUser).toHaveBeenCalled();
    expect(component.user()).toEqual(mockUser);
  });
});
```

### 2. 服务测试

```typescript
describe('UserService', () => {
  let service: UserService;
  let httpMock: HttpTestingController;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [UserService]
    });
    
    service = TestBed.inject(UserService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should fetch user', () => {
    const mockUser: User = { id: '1', name: 'Test User', email: '<EMAIL>' };

    service.getUser('1').subscribe(user => {
      expect(user).toEqual(mockUser);
    });

    const req = httpMock.expectOne('/api/users/1');
    expect(req.request.method).toBe('GET');
    req.flush(mockUser);
  });
});
```

## 代码质量工具

### 1. ESLint 配置

```json
{
  "extends": [
    "@angular-eslint/recommended",
    "@typescript-eslint/recommended"
  ],
  "rules": {
    "@typescript-eslint/no-unused-vars": "error",
    "@typescript-eslint/explicit-function-return-type": "warn",
    "@angular-eslint/component-class-suffix": "error",
    "@angular-eslint/directive-class-suffix": "error"
  }
}
```

### 2. Prettier 配置

```json
{
  "printWidth": 100,
  "tabWidth": 2,
  "useTabs": false,
  "semi": true,
  "singleQuote": true,
  "trailingComma": "es5",
  "bracketSpacing": true,
  "arrowParens": "avoid"
}
```

## 提交规范

### Git 提交消息格式

```
<type>(<scope>): <subject>

<body>

<footer>
```

#### 类型 (type)
- `feat`: 新功能
- `fix`: 修复 bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

#### 示例
```
feat(user): add user profile editing functionality

- Add edit mode toggle
- Implement form validation
- Add save/cancel actions

Closes #123
```

## 总结

遵循这些开发规范可以确保：

1. **代码一致性**: 团队成员编写风格一致的代码
2. **可维护性**: 清晰的结构和命名约定
3. **性能优化**: 使用最佳实践提高应用性能
4. **质量保证**: 完善的测试和代码检查
5. **团队协作**: 标准化的工作流程

定期回顾和更新这些规范，确保它们与 Angular 最新最佳实践保持同步。

import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { LoginComponent } from './login.component';

describe('LoginComponent', () => {
  let component: LoginComponent;
  let fixture: ComponentFixture<LoginComponent>;
  let mockRouter: jasmine.SpyObj<Router>;

  beforeEach(async () => {
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);

    await TestBed.configureTestingModule({
      imports: [LoginComponent],
      providers: [
        { provide: Router, useValue: routerSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(LoginComponent);
    component = fixture.componentInstance;
    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should update email signal when onEmailChange is called', () => {
    const testEmail = '<EMAIL>';
    component.onEmailChange(testEmail);
    expect(component.email()).toBe(testEmail);
  });

  it('should update password signal when onPasswordChange is called', () => {
    const testPassword = 'password123';
    component.onPasswordChange(testPassword);
    expect(component.password()).toBe(testPassword);
  });

  it('should not submit form when email or password is empty', () => {
    component.onEmailChange('');
    component.onPasswordChange('password');
    component.onSubmit();
    expect(component.isLoading()).toBeFalse();
  });

  it('should set loading state and navigate on successful submit', (done) => {
    component.onEmailChange('<EMAIL>');
    component.onPasswordChange('password123');
    
    component.onSubmit();
    expect(component.isLoading()).toBeTrue();

    setTimeout(() => {
      expect(component.isLoading()).toBeFalse();
      expect(mockRouter.navigate).toHaveBeenCalledWith(['/dashboard']);
      done();
    }, 1100);
  });
});

<div class="unauthorized-container">
  <div class="unauthorized-content">
    <div class="error-icon">
      <svg width="120" height="120" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="12" cy="12" r="10" stroke="#ef4444" stroke-width="2"/>
        <path d="M15 9l-6 6" stroke="#ef4444" stroke-width="2"/>
        <path d="M9 9l6 6" stroke="#ef4444" stroke-width="2"/>
      </svg>
    </div>
    
    <h1 class="error-title">访问被拒绝</h1>
    
    <p class="error-message">
      抱歉，您没有权限访问此页面。请联系管理员获取相应权限。
    </p>
    
    <div class="user-info">
      @if (currentUser()) {
        <p>当前用户: <strong>{{ currentUser()?.name }}</strong></p>
        <p>用户角色: <strong>{{ currentUser()?.role === 'admin' ? '管理员' : '普通用户' }}</strong></p>
      }
    </div>
    
    <div class="action-buttons">
      <button class="btn btn-outline" (click)="goBack()">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M19 12H5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M12 19l-7-7 7-7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        返回上页
      </button>
      
      <button class="btn btn-primary" (click)="goHome()">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <polyline points="9,22 9,12 15,12 15,22" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        回到首页
      </button>
    </div>
    
    <div class="help-text">
      <p>如果您认为这是一个错误，请联系系统管理员。</p>
    </div>
  </div>
</div>

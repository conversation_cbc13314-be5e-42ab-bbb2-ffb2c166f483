.layout-demo-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  background: #f8fafc;
  min-height: 100vh;
}

.demo-header {
  text-align: center;
  margin-bottom: 3rem;
}

.demo-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1rem;
}

.demo-description {
  font-size: 1.125rem;
  color: #64748b;
  max-width: 800px;
  margin: 0 auto;
  line-height: 1.6;
}

.layouts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

.layout-card {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
  transition: transform 0.2s, box-shadow 0.2s;
}

.layout-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.layout-name {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.layout-type {
  background: #dbeafe;
  color: #1d4ed8;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
}

.layout-description {
  color: #64748b;
  margin-bottom: 1.5rem;
  line-height: 1.5;
}

.features-section,
.use-case-section {
  margin-bottom: 1.5rem;
}

.features-section h4,
.use-case-section h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
}

.features-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.features-list li {
  padding: 0.25rem 0;
  color: #64748b;
  position: relative;
  padding-left: 1.5rem;
}

.features-list li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #10b981;
  font-weight: 600;
}

.use-case {
  color: #64748b;
  font-style: italic;
  margin: 0;
  line-height: 1.5;
}

.card-actions {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
}

.demo-button,
.code-button {
  flex: 1;
  padding: 0.75rem 1rem;
  border-radius: 6px;
  font-weight: 500;
  text-decoration: none;
  text-align: center;
  transition: all 0.2s;
  cursor: pointer;
  border: none;
  font-size: 0.875rem;
}

.demo-button {
  background: #3b82f6;
  color: white;
}

.demo-button:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

.code-button {
  background: #f1f5f9;
  color: #475569;
  border: 1px solid #e2e8f0;
}

.code-button:hover {
  background: #e2e8f0;
  color: #334155;
}

.comparison-section {
  margin-bottom: 4rem;
}

.comparison-section h2 {
  font-size: 2rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 2rem;
  text-align: center;
}

.comparison-table {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
}

.comparison-table table {
  width: 100%;
  border-collapse: collapse;
}

.comparison-table th,
.comparison-table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid #e2e8f0;
}

.comparison-table th {
  background: #f8fafc;
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.comparison-table td {
  color: #64748b;
}

.comparison-table tbody tr:hover {
  background: #f8fafc;
}

.usage-guide h2 {
  font-size: 2rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 2rem;
  text-align: center;
}

.guide-content {
  display: grid;
  gap: 2rem;
}

.guide-item {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
}

.guide-item h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 1rem;
}

.guide-item p {
  color: #64748b;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.guide-item pre {
  background: #f1f5f9;
  padding: 1rem;
  border-radius: 6px;
  overflow-x: auto;
  border: 1px solid #e2e8f0;
}

.guide-item code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  color: #374151;
}

@media (max-width: 768px) {
  .layout-demo-container {
    padding: 1rem;
  }
  
  .demo-header h1 {
    font-size: 2rem;
  }
  
  .layouts-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .layout-card {
    padding: 1.5rem;
  }
  
  .card-actions {
    flex-direction: column;
  }
  
  .comparison-table {
    overflow-x: auto;
  }
  
  .comparison-table table {
    min-width: 600px;
  }
  
  .guide-item {
    padding: 1.5rem;
  }
}

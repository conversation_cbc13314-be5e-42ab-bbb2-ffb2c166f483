<div class="dashboard-container">
  <!-- 顶部导航栏 -->
  <header class="dashboard-header">
    <div class="header-content">
      <h1>仪表板</h1>
      <div class="user-menu">
        <span class="welcome-text">欢迎, {{ currentUser() }}</span>
        <div class="user-actions">
          <button class="btn btn-outline" (click)="navigateToProfile()">
            个人资料
          </button>
          <button class="btn btn-outline" (click)="navigateToSettings()">
            设置
          </button>
          <button class="btn btn-danger" (click)="logout()">
            退出登录
          </button>
        </div>
      </div>
    </div>
  </header>

  <!-- 主要内容区域 -->
  <main class="dashboard-main">
    @if (isLoading()) {
      <div class="loading-container">
        <div class="loading-spinner"></div>
        <p>加载中...</p>
      </div>
    } @else {
      <!-- 统计卡片 -->
      <section class="stats-section">
        <h2>数据概览</h2>
        <div class="stats-grid">
          @for (card of cards(); track card.title) {
            <div class="stat-card">
              <div class="stat-icon">{{ card.icon }}</div>
              <div class="stat-content">
                <h3>{{ card.title }}</h3>
                <div class="stat-value">{{ card.value }}</div>
                <div class="stat-trend" [class]="'trend-' + card.trend">
                  @switch (card.trend) {
                    @case ('up') {
                      <span class="trend-arrow">↗</span>
                    }
                    @case ('down') {
                      <span class="trend-arrow">↘</span>
                    }
                    @default {
                      <span class="trend-arrow">→</span>
                    }
                  }
                  {{ card.trendValue }}
                </div>
              </div>
            </div>
          }
        </div>
      </section>

      <!-- 快速操作 -->
      <section class="actions-section">
        <h2>快速操作</h2>
        <div class="actions-grid">
          <button class="action-card" (click)="navigateToProfile()">
            <div class="action-icon">👤</div>
            <div class="action-title">个人资料</div>
            <div class="action-description">查看和编辑个人信息</div>
          </button>
          
          <button class="action-card" (click)="navigateToSettings()">
            <div class="action-icon">⚙️</div>
            <div class="action-title">系统设置</div>
            <div class="action-description">配置应用程序设置</div>
          </button>
          
          <button class="action-card">
            <div class="action-icon">📊</div>
            <div class="action-title">数据报告</div>
            <div class="action-description">查看详细的数据分析</div>
          </button>
          
          <button class="action-card">
            <div class="action-icon">🔔</div>
            <div class="action-title">通知中心</div>
            <div class="action-description">管理系统通知</div>
          </button>
        </div>
      </section>
    }
  </main>
</div>

.blank-layout {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #ffffff;
}

/* 确保子组件可以占满整个空间 */
.blank-layout > * {
  flex: 1;
  width: 100%;
  height: 100%;
}

/* 移除默认的边距和填充 */
.blank-layout {
  margin: 0;
  padding: 0;
  overflow: hidden;
}

/* 支持全屏内容 */
.blank-layout ::ng-deep .fullscreen-content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
}

/* 支持居中内容 */
.blank-layout ::ng-deep .centered-content {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
}

/* 支持滚动内容 */
.blank-layout ::ng-deep .scrollable-content {
  overflow-y: auto;
  height: 100vh;
}

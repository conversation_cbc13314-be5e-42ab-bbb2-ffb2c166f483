# 基于账号类型的动态登录系统指南

## 系统概述

本系统实现了基于账号类型的动态登录功能，用户可以根据不同的账号类型（管理员、普通用户、访客）获得相应的页面布局和功能权限。

## 核心特性

### 1. 多账号类型支持
- **管理员账号**: 完整的系统管理权限，使用 DefaultLayout
- **普通用户账号**: 基本的应用使用权限，使用 TopNavLayout  
- **访客账号**: 有限的浏览权限，使用 TopNavLayout

### 2. 动态布局分配
- 根据账号类型自动选择最适合的页面布局
- 同一用户可以访问使用不同布局的页面
- 布局与功能需求匹配，而非与用户角色绑定

### 3. 智能权限控制
- 基于账号类型的权限验证
- 动态路由配置根据权限过滤
- 页面级别的访问控制

## 演示账号

### 管理员账号
```
邮箱: <EMAIL>
密码: admin123
权限: 完整的系统管理权限
布局: DefaultLayout (完整导航和侧边栏)
默认路由: /admin
```

### 普通用户账号
```
邮箱: <EMAIL>
密码: user123
权限: 基本的应用使用权限
布局: TopNavLayout (简洁的顶部导航)
默认路由: /app
```

### 访客账号
```
邮箱: <EMAIL>
密码: guest123
权限: 有限的浏览权限
布局: TopNavLayout (简洁的顶部导航)
默认路由: /guest
```

## 使用指南

### 1. 登录流程

#### 步骤1: 选择账号类型
- 在登录页面选择要登录的账号类型
- 每种账号类型显示其特点和可用功能
- 选择后登录按钮会显示对应的账号类型

#### 步骤2: 使用演示账号
- 点击演示账号卡片可以快速填充登录信息
- 自动选择对应的账号类型
- 一键体验不同类型的功能

#### 步骤3: 手动输入
- 输入邮箱和密码
- 确保选择了正确的账号类型
- 点击登录按钮

### 2. 登录后体验

#### 管理员体验
- **布局**: DefaultLayout - 完整的侧边栏导航
- **功能**: 
  - 管理仪表板
  - 用户管理
  - 布局演示
  - 系统设置（权限控制）
- **导航**: 丰富的侧边栏菜单，支持折叠

#### 普通用户体验
- **布局**: TopNavLayout - 简洁的顶部导航
- **功能**:
  - 个人仪表板
  - 个人资料管理
  - 基本数据查看
- **导航**: 水平导航菜单，界面简洁

#### 访客体验
- **布局**: TopNavLayout - 简洁的顶部导航
- **功能**:
  - 公开内容浏览
  - 基本信息查看
- **导航**: 受限的导航选项

## 技术实现

### 1. 账号类型配置
```typescript
// AccountTypeService 中的配置
{
  admin: {
    type: 'admin',
    name: '管理员',
    defaultLayout: 'DefaultLayout',
    defaultRoute: '/admin',
    permissions: ['admin', 'user-management', 'system-settings']
  },
  user: {
    type: 'user', 
    name: '普通用户',
    defaultLayout: 'TopNavLayout',
    defaultRoute: '/app',
    permissions: ['user', 'profile-management', 'data-view']
  },
  guest: {
    type: 'guest',
    name: '访客',
    defaultLayout: 'TopNavLayout', 
    defaultRoute: '/guest',
    permissions: ['guest', 'public-view']
  }
}
```

### 2. 动态路由配置
```typescript
// 根据账号类型返回不同的路由配置
private getRoutesByAccountType(accountType: UserAccountType): UserRouteResponse {
  switch (accountType) {
    case 'admin':
      return this.getAdminRoutes();
    case 'guest':
      return this.getGuestRoutes();
    case 'user':
    default:
      return this.getUserRoutes();
  }
}
```

### 3. 登录流程
```typescript
// 基于账号类型的登录
loginWithAccountType(credentials: LoginCredentials): Observable<boolean> {
  return this.validateCredentials(credentials).pipe(
    switchMap(() => {
      const user = this.createUserByAccountType(credentials);
      return this.loadRoutesByAccountType(credentials.accountType);
    }),
    tap(() => {
      this.navigateToAccountTypeHome(credentials.accountType);
    })
  );
}
```

## 测试场景

### 1. 基本登录测试
- [ ] 使用管理员账号登录，验证跳转到 `/admin` 并使用 DefaultLayout
- [ ] 使用普通用户账号登录，验证跳转到 `/app` 并使用 TopNavLayout
- [ ] 使用访客账号登录，验证跳转到 `/guest` 并使用 TopNavLayout

### 2. 权限控制测试
- [ ] 管理员可以访问用户管理页面
- [ ] 普通用户无法访问管理员功能
- [ ] 访客只能访问公开内容

### 3. 布局切换测试
- [ ] 验证不同账号类型使用不同的布局
- [ ] 验证布局响应式设计在移动端正常工作
- [ ] 验证导航菜单根据权限正确显示

### 4. 会话管理测试
- [ ] 登录后刷新页面，验证状态保持
- [ ] 退出登录后验证路由清除
- [ ] 验证本地存储正确管理用户信息

## 扩展指南

### 1. 添加新账号类型
1. 在 `AccountTypeService` 中添加新的账号类型配置
2. 在 `DynamicRouteService` 中添加对应的路由配置方法
3. 更新演示账号列表
4. 测试新账号类型的功能

### 2. 自定义布局
1. 创建新的布局组件
2. 在 `DynamicRouteService` 中注册布局映射
3. 在账号类型配置中指定新布局
4. 测试布局功能

### 3. 权限扩展
1. 在账号类型配置中添加新权限
2. 在路由配置中使用新权限
3. 在组件中实现权限检查
4. 测试权限控制

## 故障排除

### 常见问题

1. **登录后页面空白**
   - 检查路由配置是否正确
   - 验证组件映射是否存在
   - 查看浏览器控制台错误

2. **权限验证失败**
   - 检查用户权限配置
   - 验证路由权限设置
   - 确认权限守卫正常工作

3. **布局显示异常**
   - 检查布局组件是否正确加载
   - 验证CSS样式是否正确
   - 确认响应式设计配置

### 调试技巧

1. **查看用户状态**
   ```typescript
   // 在浏览器控制台中查看当前用户
   console.log(JSON.parse(localStorage.getItem('currentUser')));
   ```

2. **查看路由配置**
   ```typescript
   // 在组件中注入Router并查看配置
   console.log(this.router.config);
   ```

3. **查看权限信息**
   ```typescript
   // 查看当前用户权限
   console.log(this.userService.getCurrentUser()?.permissions);
   ```

这个基于账号类型的动态登录系统为您的 Angular 应用提供了灵活、安全、用户友好的认证和授权解决方案！

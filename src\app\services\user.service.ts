import { Injectable, signal, inject } from '@angular/core';
import { Router } from '@angular/router';
import { Observable, of, delay, switchMap, tap, map, catchError, throwError } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { DynamicRouteService } from './dynamic-route.service';
import { AccountTypeService } from './account-type.service';
import { UserAccountType, LoginCredentials, LoginResponse, UserInfo, RoleConfig } from '../models/dynamic-route.model';

export interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  permissions: string[];
  avatar?: string;
  phone?: string;
  department?: string;
  position?: string;
  joinDate?: string;
}

@Injectable({
  providedIn: 'root'
})
export class UserService {
  private router = inject(Router);
  private http = inject(HttpClient);
  private dynamicRouteService = inject(DynamicRouteService);
  private accountTypeService = inject(AccountTypeService);

  // 用户状态管理
  private currentUser = signal<User | null>(null);
  private userRoles = signal<string[]>([]);
  private userPermissions = signal<string[]>([]);
  private currentRoleConfig = signal<RoleConfig | null>(null);
  private isLoggedIn = signal<boolean>(false);

  // 公共只读访问器
  readonly getCurrentUser = this.currentUser.asReadonly();
  readonly getUserRoles = this.userRoles.asReadonly();
  readonly getUserPermissions = this.userPermissions.asReadonly();
  readonly getCurrentRoleConfig = this.currentRoleConfig.asReadonly();
  readonly getIsLoggedIn = this.isLoggedIn.asReadonly();

  constructor() {
    // 检查本地存储中是否有用户信息
    this.loadUserFromStorage();
  }

  /**
   * 动态角色登录（新版本）
   */
  login(credentials: LoginCredentials): Observable<boolean> {
    return this.authenticateUser(credentials).pipe(
      switchMap(loginResponse => {
        // 保存用户信息
        this.setCurrentUser(loginResponse.user);

        // 获取角色映射配置
        return this.accountTypeService.getRoleMappingConfig().pipe(
          map(roleMapping => ({ loginResponse, roleMapping }))
        );
      }),
      switchMap(({ loginResponse, roleMapping }) => {
        // 确定用户的主要角色和配置
        const primaryRole = this.accountTypeService.selectPrimaryRole(
          loginResponse.user.roles,
          roleMapping
        );
        const roleConfig = this.accountTypeService.getRoleConfig(primaryRole, roleMapping);

        // 保存角色配置
        this.setCurrentRoleConfig(roleConfig);

        // 聚合用户权限
        const aggregatedPermissions = this.accountTypeService.aggregatePermissions(
          loginResponse.user.roles,
          loginResponse.user.permissions,
          roleMapping
        );
        this.setUserPermissions(aggregatedPermissions);
        this.setUserRoles(loginResponse.user.roles);

        // 加载对应的动态路由
        return this.dynamicRouteService.loadRoutesByRole(primaryRole, loginResponse.user).pipe(
          tap(() => {
            // 导航到默认页面
            this.router.navigate([roleConfig.defaultRoute]);
          })
        );
      }),
      map(() => true),
      catchError(error => {
        console.error('Login failed:', error);
        return throwError(() => new Error('登录失败，请检查用户名和密码'));
      })
    );
  }

  /**
   * 传统登录方法（兼容性）
   */
  loginLegacy(email: string, password: string): Observable<boolean> {
    return this.login({ email, password });
  }

  /**
   * 用户认证（模拟后端API）
   */
  private authenticateUser(credentials: LoginCredentials): Observable<LoginResponse> {
    // 模拟后端认证API
    return of(null).pipe(
      delay(1000), // 模拟网络延迟
      map(() => {
        // 模拟不同的用户角色
        const mockUsers = this.getMockUsers();
        const user = mockUsers.find(u => u.email === credentials.email);

        if (!user || user.password !== credentials.password) {
          throw new Error('用户名或密码错误');
        }

        return {
          success: true,
          user: {
            id: user.id,
            name: user.name,
            email: user.email,
            avatar: user.avatar,
            roles: user.roles,
            permissions: user.permissions,
            profile: user.profile
          },
          token: `mock_token_${Date.now()}`,
          expiresIn: 3600
        } as LoginResponse;
      })
    );
  }

  /**
   * 获取模拟用户数据
   */
  private getMockUsers() {
    return [
      {
        id: 'admin_001',
        name: '系统管理员',
        email: '<EMAIL>',
        password: 'admin123',
        avatar: 'https://ui-avatars.com/api/?name=Admin&background=dc2626&color=fff',
        roles: ['admin', 'manager'],
        permissions: ['admin', 'user-management', 'system-settings', 'data-analytics'],
        profile: { department: '技术部', position: '系统管理员' }
      },
      {
        id: 'manager_001',
        name: '部门经理',
        email: '<EMAIL>',
        password: 'manager123',
        avatar: 'https://ui-avatars.com/api/?name=Manager&background=2563eb&color=fff',
        roles: ['manager', 'editor'],
        permissions: ['management', 'team-lead', 'edit-content', 'view-data'],
        profile: { department: '产品部', position: '产品经理' }
      },
      {
        id: 'user_001',
        name: '普通用户',
        email: '<EMAIL>',
        password: 'user123',
        avatar: 'https://ui-avatars.com/api/?name=User&background=059669&color=fff',
        roles: ['user'],
        permissions: ['user', 'profile-management', 'data-view'],
        profile: { department: '技术部', position: '开发工程师' }
      },
      {
        id: 'guest_001',
        name: '访客用户',
        email: '<EMAIL>',
        password: 'guest123',
        avatar: 'https://ui-avatars.com/api/?name=Guest&background=6b7280&color=fff',
        roles: ['guest'],
        permissions: ['guest', 'public-view'],
        profile: { department: '外部', position: '访客' }
      }
    ];
  }

  /**
   * 设置当前用户
   */
  private setCurrentUser(user: UserInfo): void {
    const userWithCompatibility: User = {
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.roles[0] || 'user', // 兼容性：使用第一个角色作为主角色
      permissions: user.permissions,
      avatar: user.avatar,
      phone: user.profile?.phone,
      department: user.profile?.department,
      position: user.profile?.position,
      joinDate: user.profile?.joinDate
    };

    this.currentUser.set(userWithCompatibility);
    this.isLoggedIn.set(true);
    this.saveUserToStorage(userWithCompatibility);
  }

  /**
   * 设置用户角色
   */
  private setUserRoles(roles: string[]): void {
    this.userRoles.set(roles);
  }

  /**
   * 设置用户权限
   */
  private setUserPermissions(permissions: string[]): void {
    this.userPermissions.set(permissions);
  }

  /**
   * 设置当前角色配置
   */
  private setCurrentRoleConfig(config: RoleConfig): void {
    this.currentRoleConfig.set(config);
  }

  /**
   * 登出用户并清除动态路由
   */
  logout(): void {
    this.currentUser.set(null);
    this.clearUserFromStorage();
    this.dynamicRouteService.clearDynamicRoutes();
    this.router.navigate(['/auth/login']);
  }

  /**
   * 更新用户信息
   */
  updateUser(userData: Partial<User>): Observable<User> {
    return of(null).pipe(
      delay(1000), // 模拟网络延迟
      tap(() => {
        const current = this.currentUser();
        if (current) {
          const updatedUser = { ...current, ...userData };
          this.currentUser.set(updatedUser);
          this.saveUserToStorage(updatedUser);
        }
      }),
      switchMap(() => of(this.currentUser()!))
    );
  }

  /**
   * 检查用户是否有特定权限
   */
  hasPermission(permission: string): boolean {
    const permissions = this.userPermissions();
    return permissions.includes(permission);
  }

  /**
   * 检查用户是否有任一权限
   */
  hasAnyPermission(permissions: string[]): boolean {
    return permissions.some(permission => this.hasPermission(permission));
  }

  /**
   * 检查用户是否有特定角色
   */
  hasRole(role: string): boolean {
    const roles = this.userRoles();
    return roles.includes(role);
  }

  /**
   * 检查用户是否有任一角色
   */
  hasAnyRole(roles: string[]): boolean {
    return roles.some(role => this.hasRole(role));
  }

  /**
   * 从本地存储加载用户信息
   */
  private loadUserFromStorage(): void {
    try {
      const userJson = localStorage.getItem('currentUser');
      if (userJson) {
        const user = JSON.parse(userJson);
        this.currentUser.set(user);

        // 如果用户已登录，加载动态路由
        this.dynamicRouteService.loadUserRoutes(user.id).subscribe({
          next: (routeResponse) => {
            this.dynamicRouteService.applyDynamicRoutes(routeResponse.routes);
          },
          error: (error) => {
            console.error('加载用户路由失败:', error);
            // 如果加载路由失败，清除用户信息
            this.logout();
          }
        });
      }
    } catch (error) {
      console.error('从本地存储加载用户信息失败:', error);
      this.clearUserFromStorage();
    }
  }

  /**
   * 保存用户信息到本地存储
   */
  private saveUserToStorage(user: User): void {
    try {
      localStorage.setItem('currentUser', JSON.stringify(user));
    } catch (error) {
      console.error('保存用户信息到本地存储失败:', error);
    }
  }

  /**
   * 清除本地存储中的用户信息
   */
  private clearUserFromStorage(): void {
    try {
      localStorage.removeItem('currentUser');
    } catch (error) {
      console.error('清除本地存储中的用户信息失败:', error);
    }
  }


}

<div class="top-nav-layout">
  <!-- 顶部导航栏 -->
  <header class="top-nav-header">
    <div class="header-container">
      <div class="header-left">
        <h1 class="app-title">
          <a routerLink="/">应用系统</a>
        </h1>
      </div>
      
      <!-- 主导航菜单 -->
      <nav class="main-nav">
        <ul class="nav-list">
          @for (item of navigationItems(); track item.id) {
            @if (hasPermission(item.permissions) && !item.children?.length) {
              <li class="nav-item">
                <a 
                  [routerLink]="item.path" 
                  routerLinkActive="active"
                  class="nav-link"
                >
                  @if (item.icon) {
                    <i class="nav-icon {{ item.icon }}"></i>
                  }
                  <span>{{ item.title }}</span>
                </a>
              </li>
            } @else if (hasPermission(item.permissions) && item.children?.length) {
              <!-- 下拉菜单 -->
              <li class="nav-item dropdown">
                <button class="nav-link dropdown-toggle">
                  @if (item.icon) {
                    <i class="nav-icon {{ item.icon }}"></i>
                  }
                  <span>{{ item.title }}</span>
                  <i class="dropdown-arrow">▼</i>
                </button>
                <ul class="dropdown-menu">
                  @for (child of item.children; track child.id) {
                    @if (hasPermission(child.permissions)) {
                      <li>
                        <a 
                          [routerLink]="child.path" 
                          routerLinkActive="active"
                          class="dropdown-link"
                        >
                          @if (child.icon) {
                            <i class="nav-icon {{ child.icon }}"></i>
                          }
                          <span>{{ child.title }}</span>
                        </a>
                      </li>
                    }
                  }
                </ul>
              </li>
            }
          }
        </ul>
      </nav>
      
      <!-- 用户信息 -->
      <div class="header-right">
        <div class="user-info">
          <span class="user-name">{{ currentUser()?.name }}</span>
          <div class="user-menu">
            <button class="user-avatar">
              {{ currentUser()?.name?.charAt(0) }}
            </button>
            <div class="user-dropdown">
              <a routerLink="/user/profile">个人资料</a>
              <a routerLink="/user/settings">设置</a>
              <button (click)="logout()">退出登录</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </header>

  <!-- 主内容区域 -->
  <main class="top-nav-main">
    <div class="main-container">
      <router-outlet></router-outlet>
    </div>
  </main>

  <!-- 底部（可选） -->
  <footer class="top-nav-footer">
    <div class="footer-container">
      <p>&copy; 2024 应用系统. 保留所有权利.</p>
    </div>
  </footer>
</div>

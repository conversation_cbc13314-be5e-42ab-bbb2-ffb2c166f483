import { Injectable, inject } from '@angular/core';
import { Observable, of, BehaviorSubject } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';
import { RoleConfig, RoleMappingResponse, UserAccountType, UserAccountTypeConfig } from '../models/dynamic-route.model';

@Injectable({
  providedIn: 'root'
})
export class AccountTypeService {
  private readonly http = inject(HttpClient);

  // 角色映射配置缓存
  private roleMappingCache = new BehaviorSubject<Record<string, RoleConfig> | null>(null);

  // 默认角色映射配置（用于降级处理）
  private readonly defaultRoleMapping: Record<string, RoleConfig> = {
    admin: {
      accountType: 'admin',
      defaultLayout: 'DefaultLayout',
      defaultRoute: '/admin',
      layoutPriority: 1,
      permissions: ['admin', 'user-management', 'system-settings']
    },
    manager: {
      accountType: 'manager',
      defaultLayout: 'DefaultLayout',
      defaultRoute: '/management',
      layoutPriority: 2,
      permissions: ['management', 'team-lead']
    },
    editor: {
      accountType: 'user',
      defaultLayout: 'TopNavLayout',
      defaultRoute: '/editor',
      layoutPriority: 3,
      permissions: ['edit-content', 'view-data']
    },
    user: {
      accountType: 'user',
      defaultLayout: 'TopNavLayout',
      defaultRoute: '/app',
      layoutPriority: 4,
      permissions: ['user', 'profile-management']
    },
    viewer: {
      accountType: 'guest',
      defaultLayout: 'TopNavLayout',
      defaultRoute: '/view',
      layoutPriority: 5,
      permissions: ['view-only']
    },
    guest: {
      accountType: 'guest',
      defaultLayout: 'TopNavLayout',
      defaultRoute: '/guest',
      layoutPriority: 6,
      permissions: ['guest', 'public-view']
    }
  };

  /**
   * 获取角色映射配置
   */
  getRoleMappingConfig(): Observable<Record<string, RoleConfig>> {
    // 如果缓存中有数据，直接返回
    if (this.roleMappingCache.value) {
      return of(this.roleMappingCache.value);
    }

    // 模拟从后端获取角色映射配置（实际项目中替换为真实API）
    return of(this.defaultRoleMapping).pipe(
      map(roleMapping => {
        this.roleMappingCache.next(roleMapping);
        return roleMapping;
      }),
      catchError(error => {
        console.warn('Failed to load role mapping config, using default:', error);
        this.roleMappingCache.next(this.defaultRoleMapping);
        return of(this.defaultRoleMapping);
      })
    );
  }

  /**
   * 根据用户角色选择主要角色配置
   */
  selectPrimaryRole(userRoles: string[], roleMapping?: Record<string, RoleConfig>): string {
    const mapping = roleMapping || this.roleMappingCache.value || this.defaultRoleMapping;

    // 过滤出有效的角色
    const validRoles = userRoles.filter(role => mapping[role]);

    if (validRoles.length === 0) {
      // 如果没有有效角色，返回默认角色
      return 'user';
    }

    // 按优先级排序，选择优先级最高的（数字最小的）
    return validRoles.sort((a, b) =>
      mapping[a].layoutPriority - mapping[b].layoutPriority
    )[0];
  }

  /**
   * 获取角色配置
   */
  getRoleConfig(role: string, roleMapping?: Record<string, RoleConfig>): RoleConfig {
    const mapping = roleMapping || this.roleMappingCache.value || this.defaultRoleMapping;

    return mapping[role] || this.getDefaultRoleConfig();
  }

  /**
   * 获取默认角色配置（用于未知角色）
   */
  getDefaultRoleConfig(): RoleConfig {
    return {
      accountType: 'unknown',
      defaultLayout: 'TopNavLayout',
      defaultRoute: '/dashboard',
      layoutPriority: 999,
      permissions: ['basic']
    };
  }

  /**
   * 聚合用户所有角色的权限
   */
  aggregatePermissions(userRoles: string[], userPermissions: string[], roleMapping?: Record<string, RoleConfig>): string[] {
    const mapping = roleMapping || this.roleMappingCache.value || this.defaultRoleMapping;
    const allPermissions = new Set(userPermissions);

    // 添加角色相关的权限
    userRoles.forEach(role => {
      if (mapping[role]?.permissions) {
        mapping[role].permissions!.forEach(permission =>
          allPermissions.add(permission)
        );
      }
    });

    return Array.from(allPermissions);
  }

  /**
   * 清除角色映射缓存
   */
  clearCache(): void {
    this.roleMappingCache.next(null);
  }

  // ============ 兼容性方法（保留用于向后兼容）============

  /**
   * 获取所有账号类型配置（兼容性方法）
   * @deprecated 使用 getRoleMappingConfig() 替代
   */
  getAllAccountTypes(): UserAccountTypeConfig[] {
    return [
      {
        type: 'admin',
        name: '管理员',
        description: '拥有完整的系统管理权限',
        defaultLayout: 'DefaultLayout',
        defaultRoute: '/admin',
        permissions: ['admin', 'user-management'],
        features: ['用户管理', '系统设置']
      },
      {
        type: 'user',
        name: '普通用户',
        description: '拥有基本的应用使用权限',
        defaultLayout: 'TopNavLayout',
        defaultRoute: '/app',
        permissions: ['user', 'profile-management'],
        features: ['个人资料', '数据查看']
      },
      {
        type: 'guest',
        name: '访客',
        description: '拥有有限的浏览权限',
        defaultLayout: 'TopNavLayout',
        defaultRoute: '/guest',
        permissions: ['guest', 'public-view'],
        features: ['公开内容浏览']
      }
    ];
  }

  /**
   * 获取指定账号类型的配置（兼容性方法）
   * @deprecated 使用 getRoleConfig() 替代
   */
  getAccountTypeConfig(type: UserAccountType): UserAccountTypeConfig {
    const configs = this.getAllAccountTypes();
    return configs.find(config => config.type === type) || configs[1]; // 默认返回 user 配置
  }

  /**
   * 获取演示账号列表（已移除）
   * @deprecated 演示账号功能已移除
   */
  getDemoAccounts() {
    return [];
  }

  /**
   * 获取账号类型的默认路由（兼容性方法）
   * @deprecated 使用 getRoleConfig() 替代
   */
  getDefaultRoute(type: UserAccountType): string {
    return this.getAccountTypeConfig(type).defaultRoute;
  }

  /**
   * 获取账号类型的默认布局（兼容性方法）
   * @deprecated 使用 getRoleConfig() 替代
   */
  getDefaultLayout(type: UserAccountType): string {
    return this.getAccountTypeConfig(type).defaultLayout;
  }

  /**
   * 验证账号类型是否有效（兼容性方法）
   * @deprecated 使用动态角色验证替代
   */
  isValidAccountType(type: string): type is UserAccountType {
    return ['admin', 'user', 'guest'].includes(type);
  }

  /**
   * 设置当前选中的账号类型（兼容性方法）
   * @deprecated 不再需要选择账号类型
   */
  setSelectedAccountType(type: UserAccountType): void {
    // 空实现，保持兼容性
  }

  /**
   * 检查账号类型是否有指定权限（兼容性方法）
   * @deprecated 使用用户实际权限检查替代
   */
  hasPermission(accountType: UserAccountType, permission: string): boolean {
    const config = this.getAccountTypeConfig(accountType);
    return config.permissions.includes(permission);
  }
}

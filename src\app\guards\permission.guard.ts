import { Injectable, inject } from '@angular/core';
import { CanActivate, Router, ActivatedRouteSnapshot } from '@angular/router';
import { UserService } from '../services/user.service';

@Injectable({
  providedIn: 'root'
})
export class PermissionGuard implements CanActivate {
  private router = inject(Router);
  private userService = inject(UserService);

  canActivate(route: ActivatedRouteSnapshot): boolean {
    const user = this.userService.getCurrentUser();
    
    if (!user) {
      this.router.navigate(['/auth/login']);
      return false;
    }

    // 从路由数据中获取所需权限
    const requiredPermissions = route.data?.['permissions'] as string[];
    const requiredRoles = route.data?.['roles'] as string[];

    // 检查权限
    if (requiredPermissions && requiredPermissions.length > 0) {
      const hasPermission = this.userService.hasAnyPermission(requiredPermissions);
      if (!hasPermission) {
        this.router.navigate(['/unauthorized']);
        return false;
      }
    }

    // 检查角色
    if (requiredRoles && requiredRoles.length > 0) {
      const hasRole = requiredRoles.some(role => this.userService.hasRole(role));
      if (!hasRole) {
        this.router.navigate(['/unauthorized']);
        return false;
      }
    }

    return true;
  }
}

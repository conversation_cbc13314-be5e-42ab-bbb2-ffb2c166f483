<div class="login-container">
  <div class="login-card">
    <div class="login-header">
      <h1>登录</h1>
      <p>欢迎回来，系统将根据您的账号自动分配相应的权限和界面</p>
    </div>

    <!-- 演示账号 -->
    <div class="demo-accounts-section">
      <h3>演示账号</h3>
      <p class="demo-description">点击下方账号快速登录体验不同角色的功能</p>
      <div class="demo-accounts-grid">
        @for (demoAccount of demoAccounts; track demoAccount.email) {
        <div class="demo-account-card" (click)="useDemoAccount(demoAccount)">
          <div class="demo-account-info">
            <strong>{{ demoAccount.name }}</strong>
            <span class="demo-email">{{ demoAccount.email }}</span>
            <p class="demo-description">{{ demoAccount.description }}</p>
          </div>
          <div class="demo-account-roles">
            @for (role of demoAccount.roles; track role) {
            <span class="role-tag">{{ role }}</span>
            }
          </div>
        </div>
        }
      </div>
    </div>

    <!-- 登录表单 -->
    <form (ngSubmit)="onSubmit()" #loginForm="ngForm" class="login-form">
      @if (errorMessage()) {
      <div class="error-message">
        {{ errorMessage() }}
      </div>
      }

      <div class="form-group">
        <label for="email">邮箱地址</label>
        <input
          type="email"
          id="email"
          name="email"
          [value]="email()"
          #emailInput
          (input)="onEmailChange(emailInput.value)"
          required
          class="form-control"
          placeholder="请输入邮箱地址"
        />
      </div>

      <div class="form-group">
        <label for="password">密码</label>
        <input
          type="password"
          id="password"
          name="password"
          [value]="password()"
          #passwordInput
          (input)="onPasswordChange(passwordInput.value)"
          required
          class="form-control"
          placeholder="请输入密码"
        />
      </div>

      <button
        type="submit"
        class="login-button"
        [disabled]="!email() || !password() || isLoading()"
      >
        @if (isLoading()) {
        <span class="loading-spinner"></span>
        登录中... } @else { 登录 }
      </button>
    </form>

    <div class="login-footer">
      <p class="login-note">💡 系统将根据您的账号自动识别角色并分配相应的权限和界面布局</p>
    </div>
  </div>
</div>

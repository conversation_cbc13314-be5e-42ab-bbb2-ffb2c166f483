<div class="default-layout">
  <!-- 顶部导航栏 -->
  <header class="layout-header">
    <div class="header-content">
      <div class="header-left">
        <button 
          class="sidebar-toggle" 
          (click)="toggleSidebar()"
          [class.collapsed]="isSidebarCollapsed()"
        >
          <span class="hamburger"></span>
        </button>
        <h1 class="app-title">应用系统</h1>
      </div>
      
      <div class="header-right">
        <!-- 移动端菜单按钮 -->
        <button 
          class="mobile-menu-toggle md:hidden" 
          (click)="toggleMobileMenu()"
        >
          <span class="hamburger"></span>
        </button>
        
        <!-- 用户信息 -->
        <div class="user-info">
          <span class="user-name">{{ currentUser()?.name }}</span>
          <div class="user-menu">
            <button class="user-avatar">
              {{ currentUser()?.name?.charAt(0) }}
            </button>
            <div class="dropdown-menu">
              <a routerLink="/user/profile">个人资料</a>
              <a routerLink="/user/settings">设置</a>
              <button (click)="logout()">退出登录</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </header>

  <div class="layout-body">
    <!-- 侧边栏 -->
    <aside 
      class="layout-sidebar" 
      [class.collapsed]="isSidebarCollapsed()"
      [class.mobile-open]="isMobileMenuOpen()"
    >
      <!-- 移动端遮罩 -->
      <div 
        class="mobile-overlay md:hidden" 
        [class.active]="isMobileMenuOpen()"
        (click)="closeMobileMenu()"
      ></div>
      
      <nav class="sidebar-nav">
        <ul class="nav-list">
          @for (item of navigationItems(); track item.id) {
            @if (hasPermission(item.permissions)) {
              <li class="nav-item">
                @if (item.children && item.children.length > 0) {
                  <!-- 有子菜单的项目 -->
                  <div class="nav-group">
                    <div class="nav-group-title">
                      @if (item.icon) {
                        <i class="nav-icon {{ item.icon }}"></i>
                      }
                      <span class="nav-text">{{ item.title }}</span>
                    </div>
                    <ul class="nav-submenu">
                      @for (child of item.children; track child.id) {
                        @if (hasPermission(child.permissions)) {
                          <li class="nav-subitem">
                            <a 
                              [routerLink]="child.path" 
                              routerLinkActive="active"
                              class="nav-link"
                              (click)="closeMobileMenu()"
                            >
                              @if (child.icon) {
                                <i class="nav-icon {{ child.icon }}"></i>
                              }
                              <span class="nav-text">{{ child.title }}</span>
                            </a>
                          </li>
                        }
                      }
                    </ul>
                  </div>
                } @else {
                  <!-- 普通菜单项 -->
                  <a 
                    [routerLink]="item.path" 
                    routerLinkActive="active"
                    class="nav-link"
                    (click)="closeMobileMenu()"
                  >
                    @if (item.icon) {
                      <i class="nav-icon {{ item.icon }}"></i>
                    }
                    <span class="nav-text">{{ item.title }}</span>
                  </a>
                }
              </li>
            }
          }
        </ul>
      </nav>
    </aside>

    <!-- 主内容区域 -->
    <main class="layout-main" [class.sidebar-collapsed]="isSidebarCollapsed()">
      <div class="main-content">
        <router-outlet></router-outlet>
      </div>
    </main>
  </div>
</div>
